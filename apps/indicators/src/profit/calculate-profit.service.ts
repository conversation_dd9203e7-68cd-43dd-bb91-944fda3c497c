import { Injectable } from '@nestjs/common';
import { DbIndicatorService } from '@app/database/db-indicator/db-indicator.service';
import PriceCalculation from '../../../../utils/PriceCalculation';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import { Strategies, StrategyIntervals } from '../../../../utils/Strategies';
import { DbBotProfitsService } from '@app/database/db-bot-profits/db-bot-profits.service';
import { addDay, addMonth, dayStart } from '@formkit/tempo';
import { DbLogService } from '@app/database/db-log/db-log.service';

@Injectable()
export class CalculateProfitService {
  constructor(
    private dbIndicatorService: DbIndicatorService,
    private dbExchangeService: DbExchangeInfoService,
    private dbBotProfitsService: DbBotProfitsService,
    private logger: DbLogService,
  ) {}

  /**
   * Только для инициальной калкуляции.
   * Не используется больше.
   *
   */
  public async calculateProfits(exchange: string) {
    const symbols =
      await this.dbExchangeService.getUsdtSymbolsWithoutExcluded(exchange);

    const strategies =
      exchange == 'gateio' ? ['ao-cross', 'nesterov'] : Strategies;
    let intervals =
      exchange == 'gateio' ? ['120', '240', '1d', '15'] : StrategyIntervals;

    for (const strategy of strategies) {
      if (strategy == 'nesterov') {
        intervals = ['15', '60'];
      }

      for (const interval of intervals) {
        this.logger.logIndicators(
          'Calculating profits',
          exchange,
          strategy,
          interval,
        );
        for (const symbol of symbols) {
          try {
            const indicators =
              await this.dbIndicatorService.getAllIndicatorsForSymbolAndStrategyAndInterval(
                symbol,
                strategy,
                interval,
                exchange,
              );

            for (let i = 1; i < indicators.length; i++) {
              const indicator = indicators[i];

              if (indicator.action == 'long' && indicator.profit == null) {
                const prevIndicator = indicators[i - 1];

                if (prevIndicator.action != 'short') {
                  continue;
                }

                const profit = PriceCalculation.toPercentGain(
                  prevIndicator.price,
                  indicator.price,
                );

                await this.dbIndicatorService.updateProfit(
                  indicator._id,
                  profit,
                );
              }

              if (indicator.action == 'short' && indicator.profit == null) {
                const prevIndicator = indicators[i - 1];

                if (prevIndicator.action != 'long') {
                  continue;
                }

                const profit = PriceCalculation.toPercentGain(
                  prevIndicator.price,
                  indicator.price,
                );

                await this.dbIndicatorService.updateProfit(
                  indicator._id,
                  profit,
                );
              }
            }
          } catch (e) {
            this.logger.errorIndicators(symbol, e);
          }
        }
      }
    }
  }

  public async updateProfits(exchange: string) {
    const from = new Date(2023, 0, 1);
    const symbols =
      await this.dbExchangeService.getUsdtSymbolsWithoutExcluded(exchange);

    this.logger.logIndicators(exchange, 'Updating strategy total profits');

    const strategies =
      exchange == 'gateio' ? ['ao-cross', 'nesterov'] : Strategies;
    let intervals =
      exchange == 'gateio' ? ['120', '240', '1d', '15'] : StrategyIntervals;
    // Update other strategies
    for (const strategy of strategies) {
      if (strategy == 'nesterov') {
        intervals = ['15', '60'];
      }

      for (const interval of intervals) {
        let allSymbolsProfit = 0;
        let allSymbolsTradesNeg = 0;
        let allSymbolsTradesPos = 0;

        this.logger.logIndicators(
          'Updating strategy total profits for',
          exchange,
          strategy,
          interval,
        );
        for (const symbol of symbols) {
          const profit =
            await this.dbIndicatorService.getProfitsForSymbolAndInterval(
              symbol,
              strategy,
              interval,
              exchange,
            );
          await this.dbBotProfitsService.dbSetProfit(
            symbol,
            strategy,
            interval,
            exchange,
            profit.total,
            profit.tradesNeg,
            profit.tradesPos,
          );

          allSymbolsProfit += profit.total;
          allSymbolsTradesNeg += profit.tradesNeg;
          allSymbolsTradesPos += profit.tradesPos;
        }

        await this.dbBotProfitsService.dbSetProfit(
          'ALL',
          strategy,
          interval,
          exchange,
          allSymbolsProfit,
          allSymbolsTradesNeg,
          allSymbolsTradesPos,
        );
      }
    }

    this.logger.logIndicators(exchange, 'Finished updating profits');
  }

  public async updateDailyProfits(exchange: string) {
    // this.logger.logIndicators(exchange, 'Updating daily profits...');
    const symbol = 'ALL';
    const endDate = new Date();
    let startDate = dayStart(addMonth(endDate, -1));
    let current = startDate;

    const symbols =
      await this.dbExchangeService.getUsdtSymbolsWithoutExcluded(exchange);

    let allSymbolsProfit = 0;
    let allSymbolsTradesNeg = 0;
    let allSymbolsTradesPos = 0;

    // Update other strategies
    const strategies =
      exchange == 'gateio' ? ['ao-cross', 'nesterov'] : Strategies;
    let intervals =
      exchange == 'gateio' ? ['120', '240', '1d', '15'] : StrategyIntervals;

    for (const strategy of strategies) {
      if (strategy == 'nesterov') {
        intervals = ['15', '60'];
      }

      for (const interval of intervals) {
        current = startDate;
        let i = 0;

        while (current <= endDate) {
          const from = current;
          const to = addDay(current, 1);
          current = to;

          const profitCheck = await this.dbBotProfitsService.dbGetDailyProfit(
            symbol,
            strategy,
            interval,
            from,
            exchange,
          );

          // Skip first 20 days if profit already calculated (performance hack)
          if (profitCheck && i < 20) {
            continue;
          }

          allSymbolsProfit = 0;
          allSymbolsTradesNeg = 0;
          allSymbolsTradesPos = 0;

          // const profitExist = await this.dbBotProfitsService.dbGetDailyProfit(
          //   symbol,
          //   strategy,
          //   interval,
          //   from,
          // );

          // if (profitExist) {
          //   continue;
          // }

          // this.logger.logIndicators(
          //   'Updating daily profits for',
          //   exchange,
          //   strategy,
          //   interval,
          //   from.toISOString(),
          // );

          for (const symbol of symbols) {
            try {
              const profit =
                await this.dbIndicatorService.calculateProfitsForDay(
                  strategy,
                  interval,
                  exchange,
                  symbol,
                  from,
                  to,
                );

              allSymbolsProfit += profit.total;
              allSymbolsTradesNeg += profit.tradesNeg;
              allSymbolsTradesPos += profit.tradesPos;
            } catch (e) {
              this.logger.errorIndicators(symbol, e);
            }
          }

          await this.dbBotProfitsService.dbSetProfit(
            symbol,
            strategy,
            interval,
            exchange,
            allSymbolsProfit,
            allSymbolsTradesNeg,
            allSymbolsTradesPos,
            from,
          );
        }
      }
    }

    // this.logger.logIndicators(exchange, 'End updating daily profits...');
  }
}

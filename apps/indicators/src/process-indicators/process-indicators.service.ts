import { Injectable } from '@nestjs/common';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';
import { DbIndicatorService } from '@app/database/db-indicator/db-indicator.service';
import { DbCandlesticksService } from '@app/database/db-candlesticks/db-candlesticks.service';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import {
  BuySellFunctionResult,
  IndicatorValue,
  MomentumIndicatorsService,
} from '../momentum/momentum-indicators.service';
import { TrendIndicatorsService } from '../trend/trend-indicators.service';
import { VolumeIndicatorsService } from '../volume/volume-indicators.service';
import { VolatilityIndicatorsService } from '../volatility/volatility-indicators.service';
import { last } from 'ramda';
import { Strategies } from '../../../../utils/Strategies';
import { DbIndicatorDocument } from '@app/database/db-indicator/db-indicator.model';
import PriceCalculation from '../../../../utils/PriceCalculation';
import { DbCoinIndicatorService } from '@app/database/db-coin-indicator/db-coin-indicator.service';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { Exchange } from '../../../../types/exchanges';

@Injectable()
export class ProcessIndicatorsService {
  constructor(
    private readonly candleService: DbCandlesticksService,
    private readonly coinPriceService: DbCoinPriceService,
    private readonly dbIndicatorService: DbIndicatorService,
    private trendIndicatorsService: TrendIndicatorsService,
    private momentumIndicatorsService: MomentumIndicatorsService,
    private volumeIndicatorsService: VolumeIndicatorsService,
    private volatilityIndicatorsService: VolatilityIndicatorsService,
    private coinIndicatorService: DbCoinIndicatorService,
    private logger: DbLogService,
  ) {}

  async executeForSymbols(
    interval: string,
    symbols: string[],
    exchange: string,
  ) {
    const registeredIndicators = new Map<string, IndicatorValue>();
    await this.trendIndicatorsService.registerIndicators(registeredIndicators);
    await this.momentumIndicatorsService.registerIndicators(
      registeredIndicators,
    );
    await this.volumeIndicatorsService.registerIndicators(registeredIndicators);
    await this.volatilityIndicatorsService.registerIndicators(
      registeredIndicators,
    );

    let strategies = Strategies;
    if (exchange == Exchange.GATEIO) {
      strategies = [
        'ao-cross',
        'ao-swing',
        'ao-cross-swing',
        'btc-cross',
        'btc-swing',
        'btc-cross-swing',
      ];
    }

    const btcAo = (
      await this.coinIndicatorService.getIndicatorsForSymbol(
        'BTCUSDT',
        interval,
        exchange,
      )
    )?.ao;
    const btcAoTrend = (
      await this.coinIndicatorService.getIndicatorsForSymbol(
        'BTCUSDT',
        interval,
        exchange,
      )
    )?.aoTrend;

    for (const symbol of symbols) {
      try {
        const candles = await this.candleService.dbGetLastCandles(
          symbol,
          interval,
          exchange,
          100,
        );

        for (const indicator of strategies) {
          const strat = registeredIndicators.get(indicator);

          if (registeredIndicators.has(indicator)) {
            await this.process({
              symbol,
              candles,
              btcAo,
              btcAoTrend,
              buyFunction: strat.buyFunction,
              sellFunction: strat.sellFunction,
              indicator,
              interval,
              exchange,
            });
          }
        }
      } catch (e) {
        this.logger.errorIndicators(symbol, e);
      }
    }
  }

  async process(args: {
    symbol: string;
    candles: DbCandleStickDocument[];
    btcAo: number;
    btcAoTrend: string;
    buyFunction: Function;
    sellFunction: Function;
    indicator: string;
    interval: string;
    exchange: string;
  }) {
    const {
      symbol,
      candles,
      btcAo,
      btcAoTrend,
      buyFunction,
      sellFunction,
      indicator,
      interval,
      exchange,
    } = args;

    let lastAction = await this.dbIndicatorService.getLastIndicatorAction(
      symbol,
      interval,
      indicator,
      exchange,
    );

    switch (lastAction?.action) {
      case 'long':
        await this.checkForSell(
          sellFunction,
          symbol,
          interval,
          indicator,
          candles,
          btcAo,
          btcAoTrend,
          lastAction,
          exchange,
        );
        break;
      case 'short':
        await this.checkForBuy(
          buyFunction,
          symbol,
          interval,
          indicator,
          candles,
          btcAo,
          btcAoTrend,
          lastAction,
          exchange,
        );
        break;
    }
  }

  private async checkForSell(
    sellFunction: Function,
    symbol: string,
    interval: string,
    indicator: string,
    candles: DbCandleStickDocument[],
    btcAo: number,
    btcAoTrend: string,
    lastAction: DbIndicatorDocument,
    exchange: string,
  ) {
    try {
      const currentCoinPrice = await this.coinPriceService.dbGetCoinPrice(
        symbol,
        exchange,
      );

      const result: BuySellFunctionResult = await sellFunction({
        candles,
        btcAo,
        btcAoTrend,
        price: currentCoinPrice,
      });

      if (result?.sell) {
        const profit = PriceCalculation.toPercentGain(
          lastAction.price,
          currentCoinPrice,
        );

        await this.dbIndicatorService.addIndicator({
          symbol,
          indicator,
          action: 'short',
          price: currentCoinPrice,
          candleTime: last(candles)?.openTime,
          indicatorValue: result.indicatorValue,
          timestamp: new Date(),
          interval,
          profit,
          exchange: exchange,
          newAdded: true,
        });
      }
    } catch (e) {
      this.logger.errorIndicators(e.message, e);
    }
  }

  private async checkForBuy(
    buyFunction: Function,
    symbol: string,
    interval: string,
    indicator: string,
    candles: DbCandleStickDocument[],
    btcAo: number,
    btcAoTrend: string,
    lastAction: DbIndicatorDocument,
    exchange: string,
  ) {
    const currentCoinPrice = await this.coinPriceService.dbGetCoinPrice(
      symbol,
      exchange,
    );

    const result: BuySellFunctionResult = await buyFunction({
      candles,
      btcAo,
      btcAoTrend,
      price: currentCoinPrice,
    });

    if (result?.buy) {
      await this.dbIndicatorService.addIndicator({
        symbol,
        indicator,
        action: 'long',
        price: currentCoinPrice,
        indicatorValue: result.indicatorValue,
        timestamp: new Date(),
        candleTime: last(candles)?.openTime,
        interval,
        profit: null,
        exchange: exchange,
        newAdded: true,
      });
    }
  }
}

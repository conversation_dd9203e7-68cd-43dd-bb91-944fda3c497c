import { Injectable } from '@nestjs/common';
import { calcOBVFromCandles } from '../../../../indicators/OBV';

//
// Volume indicators
//
@Injectable()
export class VolumeIndicatorsService {
  constructor() {}

  async registerIndicators(registered: Map<string, object>) {
    /**
     * On-Balance Volume (OBV)
     *
     * The On-Balance Volume (OBV) is an indicator in crypto trading that uses
     * volume flow to forecast price changes, aggregating buy and
     * sell volumes for a snapshot of market sentiment.
     *
     * A rising OBV indicates increased buying and potential uptrends,
     * while a falling OBV suggests rising selling pressure and possible downtrends.
     *
     * Useful in confirming or predicting trend reversals,
     * a discrepancy between a cryptocurrency’s price trend
     * and OBV movement can signal a lack of support for the current trend.
     */
    // this.onOBV(registered);
    /**
     * Volume Rate of Change (VROC)
     *
     * The Volume Rate of Change (VROC) indicator assesses how rapidly trading
     * volume changes, pinpointing quick increases or decreases.
     *
     * It’s key for spotting breakouts, as a sharp volume rise with little price
     * change may signal an upcoming major price move.
     *
     * VROC also aids in confirming trend continuations;
     * a rising price with a growing VROC suggests bullishness,
     * while a falling price with increasing VROC indicates bearishness.
     */
    //VWAP
    // adx
    //patterns bearish
    //patterns bullish
    // await this.patternIndicatorsService.findBullish(interval);
    /**
     * Trend and momentum combination:
     *
     * Pairing a trend indicator like the MACD with a momentum indicator like the RSI can be powerful.
     * For example, if MACD shows a bullish crossover while RSI moves above 30 (rising from oversold conditions),
     * it could reinforce a strong buy signal.
     *
     * Volatility and volume analysis: Using Bollinger Bands with the OBV can help traders understand market dynamics.
     * A price breakout from Bollinger Bands accompanied by a rising OBV can indicate strong buyer interest,
     * confirming the breakout’s strength.
     *
     * Trend and volume correlation: Combining trend indicators like EMAs with volume indicators can help confirm
     * the strength of a trend. For instance, a rising EMA along with increasing volume can
     * suggest a strong and healthy uptrend.
     */
  }

  private onOBV(registered: Map<string, object>) {
    const indicator = 'obv';

    const buyFunction = ({ candles, price }) => {
      const result = calcOBVFromCandles(candles).slice(-2);
      const obv1 = result.at(0)?.obv;
      const obv2 = result.at(1)?.obv;

      const buy = obv2 > obv1;

      return {
        buy,
        indicatorValue: obv2,
      };
    };

    const sellFunction = ({ candles, price }) => {
      const result = calcOBVFromCandles(candles).slice(-2);
      const obv1 = result.at(0)?.obv;
      const obv2 = result.at(1)?.obv;

      const sell = obv2 < obv1;

      return {
        sell,
        indicatorValue: obv2,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }
}

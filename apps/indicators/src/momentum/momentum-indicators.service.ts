import { Injectable } from '@nestjs/common';
import { last } from 'ramda';
import { calcRSIFromCandles } from '../../../../indicators/RSI';
import { calcSOFromCandles } from '../../../../indicators/SO';

export interface BuySellFunctionResult {
  buy: boolean;
  sell: boolean;
  indicatorValue: number;
}

export interface IndicatorValue {
  buyFunction: Function;
  sellFunction: Function;
}

//
// Momentum indicators
//
@Injectable()
export class MomentumIndicatorsService {
  constructor() {}

  async registerIndicators(registered: Map<string, IndicatorValue>) {
    /**
     * Relative Strength Index (RSI)
     *
     * The RSI is a momentum indicator that measures the speed and change of price movements,
     * typically over a 14-day period. It oscillates between 0 and 100.
     *
     * An RSI above 70 indicates that a coin is overbought, suggesting that it might
     * be due for a price correction or reversal. Conversely, an RSI below 30 suggests that a coin is oversold, potentially signalling an upcoming price increase.
     *
     * RSI is particularly useful in the volatile crypto market as it can help traders
     * spot potential reversal points by identifying extreme market conditions.
     */
    // this.onRSI(registered);
    /**
     * Stochastic Oscillator
     *
     * The Stochastic Oscillator, scaling from 0 to 100 like the RSI, measures price momentum over a specific period.
     * Key levels are 20 and 80, with readings above 80 indicating an overbought market and below 20 suggesting oversold conditions.
     *
     * In crypto trading, a move above 80 followed by a decline signals a potential sell,
     * while a dip below 20 with a subsequent rise indicates a buying opportunity.
     *
     * This tool is especially effective in identifying reversals in volatile or range-bound crypto markets.
     */
    // this.onSO(registered);
    //Ichimoku
    //ADX
  }

  private onRSI(registered: Map<string, IndicatorValue>) {
    const indicator = 'rsi';

    const buyFunction = ({ candles }) => {
      const result = calcRSIFromCandles(candles);
      const rsi = last(result)?.rsi;

      const buy = rsi < 30;

      return {
        buy,
        indicatorValue: rsi,
      };
    };

    const sellFunction = ({ candles }) => {
      const result = calcRSIFromCandles(candles);
      const rsi = last(result)?.rsi;

      const sell = rsi > 70;

      return {
        sell,
        indicatorValue: rsi,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onSO(registered: Map<string, IndicatorValue>) {
    const indicator = 'so';

    const buyFunction = ({ candles }) => {
      const result = calcSOFromCandles(candles);
      const so = last(result)?.k;

      const buy = so < 20;

      return {
        buy,
        indicatorValue: so,
      };
    };

    const sellFunction = ({ candles }) => {
      const result = calcSOFromCandles(candles);
      const so = last(result)?.k;

      const sell = so > 80;

      return {
        sell,
        indicatorValue: so,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }
}

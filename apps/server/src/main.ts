import { NestFactory } from '@nestjs/core';
import { ServerModule } from './server.module';
import { ServerService } from './server.service';
import { ConfigService } from '@nestjs/config';
import * as session from 'express-session';
import * as passport from 'passport';
import { sessionConfig } from './config/session.config';
import { NestExpressApplication } from '@nestjs/platform-express';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(ServerModule);
  const config = app.get(ConfigService);

  enableCors(config, app);
  setupSession(config, app);
  setupProxy(app);

  const service = app.get(ServerService);

  service.start();

  await app.listen(config.get('PORT') || 4000);
}

function setupSession(config: ConfigService, app: NestExpressApplication) {
  const sessionCfg = sessionConfig(config.get('SESSION_SECRET'));
  app.use(session(sessionCfg));
  app.use(passport.initialize());
  app.use(passport.session());
}

function enableCors(config: ConfigService, app: NestExpressApplication) {
  app.enableCors({
    credentials: true,
    origin: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    allowedHeaders: 'Content-Type,Authorization,true',
  });
}

function setupProxy(app: NestExpressApplication) {
  app.set('trust proxy', true);
}

bootstrap();

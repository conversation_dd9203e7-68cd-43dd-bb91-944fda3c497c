import { Module } from '@nestjs/common';
import { CacheModule } from '@nestjs/cache-manager';
import { redisStore } from 'cache-manager-redis-store';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from '@app/database';
import { ScheduleModule } from '@nestjs/schedule';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriverConfig } from '@nestjs/apollo';
import { BinanceApiModule } from '@app/binance-api';
import { ServerService } from './server.service';
import { AuthModule } from './auth/auth.module';
import { JobsService } from './jobs.service';
import { ExchangeModule } from './exchange/exchange.module';
import { UserModule } from './user/user.module';
import { BotModule } from './bot/bot.module';
import { SessionModule } from './session/session.module';
import { graphqlConfig } from './config/graphql.config';
import { WebsocketModule } from './websocket/websocket.module';
import { PumpModule } from './pump/pump.module';
import { IndicatorsModule } from './indicators/indicators.module';
import { StrategytesterModule } from './strategyTester/strategytester.module';
import { InstrumentsModule } from './instruments/instruments.module';
import { GateioApiModule } from '@app/gateio-api';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    CacheModule.register({
      isGlobal: true,
      store: redisStore({}) as any,
      host: '127.0.0.1',
      port: 6379,
    }),
    GraphQLModule.forRoot<ApolloDriverConfig>(graphqlConfig()),
    ScheduleModule.forRoot(),
    DatabaseModule,
    BinanceApiModule,
    GateioApiModule,
    AuthModule,
    ExchangeModule,
    UserModule,
    BotModule,
    WebsocketModule,
    SessionModule,
    PumpModule,
    IndicatorsModule,
    StrategytesterModule,
    InstrumentsModule,
  ],
  providers: [ServerService, JobsService],
})
export class ServerModule {}

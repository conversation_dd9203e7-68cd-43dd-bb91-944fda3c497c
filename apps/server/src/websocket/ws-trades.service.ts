import { v4 } from 'uuid';
import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { BinanceWsTradesService } from '@app/binance-api/binance-api/ws/binance-ws-trades.service';
import {
  MARKET_HISTORY_SUBSCRIPTION,
  MARKET_HISTORY_SYMBOL,
  pubsub,
} from '../../../../utils/pubSub';

@Injectable()
export class WsTradesService {
  constructor(
    private logger: DbLogService,
    private wsTradesService: BinanceWsTradesService,
  ) {}

  async initWSTrades() {
    const requestedMarketHistorySymbols = new Set();
    requestedMarketHistorySymbols.add('BTCUSDT');

    const tradesMap = {};

    await pubsub.subscribe(MARKET_HISTORY_SYMBOL, (symbol: string) => {
      if (requestedMarketHistorySymbols.has(symbol)) {
        return;
      }

      requestedMarketHistorySymbols.add(symbol);

      setInterval(() => {
        pubsub.publish(MARKET_HISTORY_SUBSCRIPTION, tradesMap);
      }, 5000);

      try {
        this.wsTradesService.trades([symbol], (ticker: any) => {
          if (!tradesMap.hasOwnProperty(ticker.symbol)) {
            tradesMap[ticker.symbol] = [];
          }
          const trades = tradesMap[ticker.symbol];
          trades.push(ticker);
          tradesMap[ticker.symbol] = trades.slice(-10).map((t) => {
            return { ...t, rowId: v4() };
          });
        });
      } catch (e) {
        this.logger.errorServer(e);
      }
    });
  }
}

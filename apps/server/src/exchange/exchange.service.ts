import { Injectable } from '@nestjs/common';
import { DbKeyService } from '@app/database/db-key/db-key.service';
import {
  BinanceAccountInfoService,
  MbAccountInfo,
} from '@app/binance-api/binance-account-info/binance-account-info.service';
import {
  Candle,
  DailyStats,
  ExchangeSymbol,
  OpenOrder,
} from '../../../../types/schema';
import { AuthUser } from '../auth/jwt.user.decorator';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { BinanceMyTradesService } from '@app/binance-api/binance-my-trades/binance-my-trades.service';
import { descend, last, prop, sortBy, sortWith } from 'ramda';
import { BinanceOpenOrdersService } from '@app/binance-api/binance-open-orders/binance-open-orders.service';
import { DbBotOrderService } from '@app/database/db-bot-order/db-bot-order.service';
import { BinanceDailyStatsService } from '@app/binance-api/binance-daily-stats/binance-daily-stats.service';
import { DbCandlesticksService } from '@app/database/db-candlesticks/db-candlesticks.service';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import { MyTrade, Order } from 'binance-api-node';
import { BinanceNewOrderService } from '@app/binance-api/binance-new-order/binance-new-order.service';
import { BinanceCancelOpenOrderService } from '@app/binance-api/binance-cancel-open-order/binance-cancel-open-order.service';
import { DbCommentService } from '@app/database/db-comment/db-comment.service';
import {
  DEPTH_NEW_SYMBOL,
  MARKET_HISTORY_SYMBOL,
  pubsub,
  TICKER_NEW_SYMBOL,
} from '../../../../utils/pubSub';
import { v4 } from 'uuid';
import { BinanceOrderBookService } from '@app/binance-api/binance-order-book/binance-order-book.service';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbTradeService } from '@app/database/db-trade/db-trade.service';
import { WsAllTickersService } from '../websocket/ws-all-tickers.service';
import { DbAllTickerService } from '@app/database/db-all-ticker/db-all-ticker.service';
import { Exchange } from '../../../../types/exchanges';
import { DbUserService } from '@app/database/db-user/db-user.service';
import { GateioAccountInfoService } from '@app/gateio-api/gateio-account-info/gateio-account-info.service';
import { GateioMyTradesService } from '@app/gateio-api/gateio-my-trades/gateio-my-trades.service';
import { GateioCancelOpenOrderService } from '@app/gateio-api/gateio-cancel-open-order/gateio-cancel-open-order.service';
import { GateioOrderBookService } from '@app/gateio-api/gateio-order-book/gateio-order-book.service';
import { GateioOpenOrdersService } from '@app/gateio-api/gateio-open-orders/gateio-open-orders.service';
import { GateioNewOrderService } from '@app/gateio-api/gateio-new-order/gateio-new-order.service';
import { GateioDailyStatsService } from '@app/gateio-api/gateio-daily-stats/gateio-daily-stats.service';
import { DbAllTicker } from '@app/database/db-all-ticker/db-all-ticker.model';
import IWSTicker = GQL.IWSTicker;

@Injectable()
export class ExchangeService {
  constructor(
    private keyService: DbKeyService,
    private binanceAccountInfo: BinanceAccountInfoService,
    private gateioAccountInfo: GateioAccountInfoService,
    private coinPriceService: DbCoinPriceService,
    private binanceTradesService: BinanceMyTradesService,
    private gateioTradesService: GateioMyTradesService,
    private binanceOpenOrdersService: BinanceOpenOrdersService,
    private botOrderService: DbBotOrderService,
    private binanceDailyStatsService: BinanceDailyStatsService,
    private gateioDailyStatsService: GateioDailyStatsService,
    private candleService: DbCandlesticksService,
    private exchangeInfoService: DbExchangeInfoService,
    private binanceNewOrderService: BinanceNewOrderService,
    private gateioNewOrderService: GateioNewOrderService,
    private binanceCancelOrderService: BinanceCancelOpenOrderService,
    private dbCommentService: DbCommentService,
    private binanceOrderBookService: BinanceOrderBookService,
    private logger: DbLogService,
    private tradeService: DbTradeService,
    private wsAllTickers: WsAllTickersService,
    private dbAllTickerService: DbAllTickerService,
    private userService: DbUserService,
    private gateioCancelOrderService: GateioCancelOpenOrderService,
    private gateioOrderBookService: GateioOrderBookService,
    private gateioOpenOrderService: GateioOpenOrdersService,
    private dbExchangeInfoService: DbExchangeInfoService,
  ) {}

  public async accountInfo(
    noSmallAmount: boolean,
    user: AuthUser,
  ): Promise<MbAccountInfo | {}> {
    const exchange = await this.userService.getUserExchange(user.userId);
    const apiKey = await this.keyService.getApiKey(user.userId, exchange);
    if (apiKey) {
      switch (exchange) {
        case Exchange.BINANCE:
          return this.binanceAccountInfo.getAccountInfo(apiKey, {
            noSmallAmount,
          });
        case Exchange.GATEIO:
          return await this.gateioAccountInfo.getAccountInfo(apiKey, {
            noSmallAmount,
          });
      }
    }

    return {};
  }

  public async prices(userId: string) {
    try {
      const exchange = await this.userService.getUserExchange(userId);

      return await this.coinPriceService.dbGetCoinPrices(exchange);
    } catch (e) {
      throw Error('Error fetching coin prices');
    }
  }

  public async mytrades(
    args: GQL.IMytradesOnExchangeQueryArguments,
    userId: string,
  ): Promise<MyTrade[]> {
    const exchange = await this.userService.getUserExchange(userId);

    const apiKey = await this.keyService.getApiKey(userId, exchange);
    let myTrades: MyTrade[];
    switch (exchange) {
      case Exchange.BINANCE:
        myTrades = await this.binanceTradesService.getMyTrades(apiKey, args);
        break;
      case Exchange.GATEIO:
        myTrades = await this.gateioTradesService.getMyTrades(apiKey, args);
        break;
    }
    const bnbPrice = await this.coinPriceService.dbGetCoinPrice(
      'BNBUSDT',
      exchange,
    );

    const tradeMap = new Map();
    myTrades.forEach((myTradePart) => {
      const tradeKey =
        String(myTradePart.orderId) +
        String(myTradePart.time) +
        String(myTradePart.isBuyer) +
        String(myTradePart.isMaker) +
        String(myTradePart.isBestMatch);

      let tradeMapValue = tradeMap.get(tradeKey);
      if (tradeMapValue) {
        tradeMapValue.qty = Number(tradeMapValue.qty) + Number(myTradePart.qty);
        tradeMapValue.commission =
          Number(tradeMapValue.commission) + Number(myTradePart.commission);
        tradeMapValue.commissionUsd = Number(
          Number(
            this.getCommisionPrice(tradeMapValue.commission, bnbPrice) +
              this.getCommisionPrice(myTradePart.commission, bnbPrice),
          ).toFixed(2),
        );
        tradeMapValue.quoteQty =
          Number(tradeMapValue.quoteQty) + Number(myTradePart.quoteQty);
      } else {
        tradeMapValue = { ...myTradePart };
        tradeMapValue.commissionUsd = this.getCommisionPrice(
          tradeMapValue.commission,
          bnbPrice,
        );
        tradeMapValue.trades = [];
      }

      tradeMapValue.trades.push(myTradePart);
      tradeMap.set(tradeKey, tradeMapValue);
    });

    const tmv = Array.from(tradeMap.values());
    // @ts-ignore
    return sortBy(descend(prop('time')))(tmv);
  }

  public async openOrders(
    args: GQL.IOpenOrdersOnExchangeQueryArguments,
    userId: string,
  ): Promise<OpenOrder[]> {
    const exchange = await this.userService.getUserExchange(userId);
    const apiKey = await this.keyService.getApiKey(userId, exchange);

    let orders = [];

    if (args.withBinance) {
      switch (exchange) {
        case Exchange.BINANCE:
          orders = await this.binanceOpenOrdersService.getOpenOrders(
            apiKey,
            args,
          );
          break;
        case Exchange.GATEIO:
          orders = await this.gateioOpenOrderService.getOpenOrders(
            apiKey,
            args,
          );
          break;
      }
    }
    const botOrders = await this.botOrderService.dbGetBotOrders(
      userId,
      false,
      exchange,
    );

    const botOrdersAsBinance = botOrders
      .filter((x) => !x.response && x.symbol === args.symbol)
      .map((x) => ({
        symbol: x.symbol,
        orderId: x._id.toString(),
        price: x.price,
        status: x.status,
        type: x.type,
        origQty: x.amount,
        executedQty: 0,
        time: new Date(x.addTimestamp).getTime(),
      }));
    orders = orders.concat(botOrdersAsBinance);

    return orders;
  }

  public async dailyStats(
    args: GQL.IDailyStatsOnExchangeQueryArguments,
    userId: string,
  ): Promise<DailyStats> {
    const exchange = await this.userService.getUserExchange(userId);

    switch (exchange) {
      case Exchange.BINANCE:
        return (await this.binanceDailyStatsService.getDailyStats(args)) as any;
      case Exchange.GATEIO:
        return (await this.gateioDailyStatsService.getDailyStats(args)) as any;
    }
  }

  public async lastCandle(
    args: GQL.ILastCandleOnExchangeQueryArguments,
    userId: string,
  ): Promise<Candle> {
    const exchange = await this.userService.getUserExchange(userId);

    return await this.candleService.dbGetLastCandle(
      args.symbol,
      args.interval,
      exchange,
    );
  }

  public async candles(
    args: GQL.ICandlesOnExchangeQueryArguments,
    user: AuthUser,
  ): Promise<Candle[]> {
    const interval = !args.interval ? '15' : args.interval;
    const exchange = await this.userService.getUserExchange(user.userId);

    return await this.candleService.dbGetLastCandles(
      args.symbol,
      interval,
      exchange,
      args.limit,
    );
  }

  public async exchangeInfo(
    args: GQL.IExchangeInfoOnExchangeQueryArguments,
    userId: string,
  ): Promise<ExchangeSymbol[]> {
    try {
      const exchange = await this.userService.getUserExchange(userId);

      return (await this.exchangeInfoService.getLatestExchangeInfo(
        exchange,
        args,
      )) as ExchangeSymbol[];
    } catch (e) {
      this.logger.errorServer(e.message, e);
    }
  }

  public async lastOrders(
    args: GQL.ILastOrdersOnExchangeQueryArguments,
    userId: string,
  ): Promise<any> {
    try {
      const exchange = await this.userService.getUserExchange(userId);
      const lastTrades = await this.tradeService.getLastTrades(
        userId,
        args,
        exchange,
      );
      if (lastTrades) {
        let result: Order[] = lastTrades
          .filter((x) => x.result != null)
          .map((x) => x.result);

        if (args.status) {
          const stList = args.status.split(',');
          result = result.filter((x) => stList.includes(x.status));
        }

        return result.map((x) => {
          if (x.type === 'MARKET' && x.fills) {
            x.price = last(x.fills).price;
          }

          x.orderId = x.orderId ? (x.orderId.toString() as any) : '';

          return x;
        });
      } else {
        return [];
      }
    } catch (e) {
      this.logger.errorServer(e);
    }
  }

  public async symbolTicker(
    args: GQL.ISymbolTickerOnExchangeQueryArguments,
    userId: string,
  ): Promise<GQL.IWSTicker> {
    const exchange = await this.userService.getUserExchange(userId);
    const result: DbAllTicker = await this.dbAllTickerService.getTickerData(
      args.symbol,
      exchange,
    );

    return result?.data ? result.data : null;
  }

  public async topVolumeCoins(
    args: GQL.ITopVolumeCoinsOnExchangeQueryArguments,
  ) {
    const allTickers: IWSTicker[] = this.wsAllTickers.getAllTickers();
    const filtered = allTickers.filter((x) => x.symbol.slice(-4) === 'USDT');
    // @ts-ignore
    const sortedByVolume = sortWith([descend(prop('percentVolume4h'))])(
      filtered,
    ) as IWSTicker[];
    return sortedByVolume.slice(0, args.number);
  }

  public async currentPrice(
    args: GQL.ICurrentPriceOnExchangeQueryArguments,
    userId: string,
  ) {
    const exchange = await this.userService.getUserExchange(userId);

    const result = await this.coinPriceService.dbGetCoinPrice(
      args.symbol,
      exchange,
    );
    return {
      symbol: args.symbol,
      price: result,
    };
  }

  public orderMutation = async (
    args: GQL.IOrderMutationOnMutationArguments,
    userId: string,
  ): Promise<Order> => {
    // return {
    //     ok: false,
    //     error: "Orders disabled by administrator"
    // };

    const exchange = await this.userService.getUserExchange(userId);

    try {
      const apiKey = await this.keyService.getApiKey(userId, exchange);

      if (
        args.type === 'BOT_TAKE_PROFIT' ||
        args.type === 'BOT_STOP_LOSS' ||
        args.type === 'AUTO_STOP_LOSS' ||
        args.type === 'AUTO_TAKE_PROFIT'
      ) {
        switch (exchange) {
          case Exchange.BINANCE:
            return await this.binanceNewOrderService.newBotOrder(userId, args);
          case Exchange.GATEIO:
            return await this.gateioNewOrderService.newBotOrder(userId, args);
        }
      }

      let response = null;

      switch (exchange) {
        case Exchange.BINANCE:
          response = await this.binanceNewOrderService.newOrder(
            userId,
            apiKey,
            args,
          );
          break;
        case Exchange.GATEIO:
          const exchangeInfo =
            (await this.dbExchangeInfoService.getLatestExchangeInfo(exchange, {
              symbol: args.symbol,
            })) as any;

          const currentCoinPrice = await this.coinPriceService.dbGetCoinPrice(
            args.symbol,
            exchange,
          );
          response = await this.gateioNewOrderService.newOrder(
            userId,
            apiKey,
            exchangeInfo,
            currentCoinPrice,
            args,
          );

          break;
      }

      return response;
    } catch (e) {
      this.logger.errorServer(e.message, e);

      return {
        ok: false,
        error: e.message,
      } as any;
    }
  };

  public async comment(
    args: GQL.ICommentOnExchangeQueryArguments,
    userId: string,
  ): Promise<any> {
    const result = await this.dbCommentService.getComment(args, userId);

    return {
      comment: result ? result.comment : '',
      ok: true,
    };
  }

  async orderbook(
    args: GQL.IOrderbookOnExchangeQueryArguments,
    userId: string,
  ): Promise<any> {
    try {
      const exchange = await this.userService.getUserExchange(userId);

      await pubsub.publish(DEPTH_NEW_SYMBOL, args.symbol);
      await pubsub.publish(MARKET_HISTORY_SYMBOL, args.symbol);
      await pubsub.publish(TICKER_NEW_SYMBOL, args.symbol);

      const apiKey = await this.keyService.getApiKey(userId, exchange);
      let orderbook;

      switch (exchange) {
        case Exchange.BINANCE:
          orderbook = await this.binanceOrderBookService.getOrderBook(
            apiKey,
            args,
          );
          break;

        case Exchange.GATEIO:
          orderbook = await this.gateioOrderBookService.getOrderBook(
            apiKey,
            args,
          );
          break;
      }

      orderbook.asks = orderbook.asks.map((x) => ({ ...x, rowId: v4() }));
      orderbook.bids = orderbook.bids.map((x) => ({ ...x, rowId: v4() }));

      return orderbook;
    } catch (e) {
      throw e;
    }
  }

  public async cancelOrderMutation(
    args: GQL.ICancelOrderMutationOnMutationArguments,
    userId: string,
  ): Promise<any> {
    const exchange = await this.userService.getUserExchange(userId);

    try {
      if (
        args.type === 'BOT_STOP_LOSS' ||
        args.type === 'BOT_TAKE_PROFIT' ||
        args.type === 'AUTO_STOP_LOSS' ||
        args.type === 'AUTO_TAKE_PROFIT'
      ) {
        const result = await this.botOrderService.dbDeleteBotOrder(
          userId,
          args.orderId,
        );
        return {
          ok: true,
          message: JSON.stringify(result),
        };
      }

      const apiKey = await this.keyService.getApiKey(userId, exchange);

      switch (exchange) {
        case Exchange.BINANCE:
          const result = await this.binanceCancelOrderService.cancelOpenOrder(
            apiKey,
            args,
          );

          return {
            ok: true,
            message: JSON.stringify(result),
          };

        case Exchange.GATEIO:
          const resultGateIO =
            await this.gateioCancelOrderService.cancelOpenOrder(apiKey, args);

          return {
            ok: true,
            message: JSON.stringify(resultGateIO),
          };
      }
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  }

  public async commentMutation(
    args: GQL.ICommentMutationOnMutationArguments,
    userId: string,
  ): Promise<any> {
    try {
      const result = await this.dbCommentService.saveComment(
        {
          symbol: args.symbol,
          comment: args.comment,
        },
        userId,
      );

      return {
        comment: result.comment,
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  }

  private getCommisionPrice(commission, bnbPrice: number) {
    return Number((Number(commission) * Number(bnbPrice)).toFixed(2));
  }
}

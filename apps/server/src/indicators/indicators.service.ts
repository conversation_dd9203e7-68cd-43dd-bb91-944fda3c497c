import { Injectable } from '@nestjs/common';
import { DbMacdService } from '@app/database/db-macd/db-macd.service';
import { DbUserService } from '@app/database/db-user/db-user.service';

@Injectable()
export class IndicatorsService {
  constructor(
    private macdService: DbMacdService,
    private userService: DbUserService,
  ) {}

  public async macd(args: GQL.IMacdOnIndicatorsQueryArguments, userId: string) {
    try {
      const exchange = await this.userService.getUserExchange(userId);
      const result = await this.macdService.getMACD(args, exchange);

      if (result && result.signal) {
        return { signal: result.signal, openTime: result.openTime };
      }

      return null;
    } catch (e) {
      throw e;
    }
  }

  public async macds(
    args: GQL.IMacdsOnIndicatorsQueryArguments,
    userId: string,
  ) {
    try {
      const exchange = await this.userService.getUserExchange(userId);
      const result = await this.macdService.getMACD(args, exchange);

      if (result && result.values) {
        return result.values;
      }

      return null;
    } catch (e) {
      throw e;
    }
  }
}

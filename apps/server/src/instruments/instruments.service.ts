import { Injectable } from '@nestjs/common';
import { DbCoinIndicatorService } from '@app/database/db-coin-indicator/db-coin-indicator.service';
import { DbUserService } from '@app/database/db-user/db-user.service';

@Injectable()
export class InstrumentsService {
  constructor(
    private coinIndicatorService: DbCoinIndicatorService,
    private userService: DbUserService,
  ) {}

  public async fetch(
    args: GQL.IFetchOnInstrumentsQueryArguments,
    userId: string,
  ) {
    try {
      const exchange = await this.userService.getUserExchange(userId);
      return await this.coinIndicatorService.getAllIndicators(args, exchange);
    } catch (e) {
      throw e;
    }
  }
}

import { Injectable } from '@nestjs/common';
import { DbSaldoService } from '@app/database/db-saldo/db-saldo.service';
import { DbSaldoUsdService } from '@app/database/db-saldo-usd/db-saldo-usd.service';
import { DbKeyService } from '@app/database/db-key/db-key.service';
import { BinanceAccountInfoService } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { DbUserService } from '@app/database/db-user/db-user.service';
import { DbFavoriteService } from '@app/database/db-favorite/db-favorite.service';
import { DbNoticedCoinService } from '@app/database/db-noticed-coin/db-noticed-coin.service';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbKeyDocument } from '@app/database/db-key/db-key.model';
import { Common } from '../../../../utils/Common';
import { Exchange } from '../../../../types/exchanges';
import { GateioAccountInfoService } from '@app/gateio-api/gateio-account-info/gateio-account-info.service';

@Injectable()
export class UserService {
  constructor(
    private saldoService: DbSaldoService,
    private saldoUsdService: DbSaldoUsdService,
    private keyService: DbKeyService,
    private binanceAccountInfoService: BinanceAccountInfoService,
    private gateioAccountInfoService: GateioAccountInfoService,
    private dbUserService: DbUserService,
    private favoritesService: DbFavoriteService,
    private coinPriceService: DbCoinPriceService,
    private noticedCoinService: DbNoticedCoinService,
    private logService: DbLogService,
    private dbKeyService: DbKeyService,
  ) {}

  public async saldo(args: GQL.ISaldoOnUserQueryArguments, userId: string) {
    const exchange = await this.dbUserService.getUserExchange(userId);
    return await this.saldoService.getUserSaldo(userId, exchange, args);
  }

  public async saldoUsd(
    args: GQL.ISaldoUsdOnUserQueryArguments,
    userId: string,
  ) {
    const exchange = await this.dbUserService.getUserExchange(userId);
    return await this.saldoUsdService.getUserSaldoUsd(userId, args, exchange);
  }

  public async saveCurrentSaldo(userId: string) {
    try {
      const exchange = await this.dbUserService.getUserExchange(userId);
      const apiKey = await this.keyService.getApiKey(userId, exchange);
      await this.saveAccountSaldo(userId, apiKey, exchange);

      return true;
    } catch (e) {
      return false;
    }
  }

  public async saveSplitMutation(
    args: GQL.ISaveSplitMutationOnMutationArguments,
    userId: string,
  ) {
    try {
      for (const arg of args.configs) {
        await this.dbUserService.dbSaveSplitMutation(userId, arg);
      }

      return new Common(true);
    } catch (e) {
      return new Common(false);
    }
  }

  public async favorites(userId: string) {
    const exchange = await this.dbUserService.getUserExchange(userId);
    return await this.favoritesService.dbGetFavoiteCoins(userId, exchange);
  }

  public async accountSettingsMutation(
    args: GQL.IAccountSettingsMutationOnMutationArguments,
    userId: string,
  ) {
    return await this.dbUserService.dbSaveAccountSettings(userId, args);
  }

  public async saveApiKeyMutation(
    args: GQL.ISaveApiKeyMutationOnMutationArguments,
    userId: string,
  ) {
    const exchange = await this.dbUserService.getUserExchange(userId);

    await this.keyService.changeBinanceApiKey(
      userId,
      args.key,
      args.secret,
      args.exchange,
    );
    return await this.keyService.getApiKey(userId, args.exchange);
  }

  public async saveExchangeMutation(
    args: GQL.ISaveExchangeMutationOnMutationArguments,
    userId: string,
  ) {
    await this.dbUserService.saveExchange(userId, args.exchange);
    await this.dbKeyService.activateKey(userId, args.exchange);

    return await this.keyService.getApiKey(userId, args.exchange);
  }

  public async saveFavoriteCoinMutation(
    args: GQL.ISaveFavoriteCoinMutationOnMutationArguments,
    userId: string,
  ) {
    try {
      const exchange = await this.dbUserService.getUserExchange(userId);

      await this.favoritesService.dbSaveFavoriteCoin(userId, args, exchange);
      return await this.favoritesService.dbGetFavoiteCoins(userId, exchange);
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  }

  public async saveNoticedCoinMutation(
    args: GQL.ISaveNoticedCoinMutationOnMutationArguments,
    userId: string,
  ) {
    try {
      const exchange = await this.dbUserService.getUserExchange(userId);
      return await this.noticedCoinService.saveNoticedCoin(
        userId,
        args,
        exchange,
      );
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  }

  public async deleteNoticedCoinMutation(
    args: GQL.IDeleteNoticedCoinMutationOnMutationArguments,
    userId: string,
  ) {
    try {
      const exchange = await this.dbUserService.getUserExchange(userId);
      return await this.noticedCoinService.deleteNoticedCoin(
        userId,
        args,
        exchange,
      );
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  }

  public async stopActiveCallMutation(
    args: GQL.IStopActiveCallMutationOnMutationArguments,
    userId: string,
  ) {
    try {
      const exchange = await this.dbUserService.getUserExchange(userId);
      return await this.noticedCoinService.stopActiveCall(
        userId,
        args,
        exchange,
      );
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  }

  public async addActiveCallMutation(
    args: GQL.IAddActiveCallMutationOnMutationArguments,
    userId: string,
  ) {
    try {
      const exchange = await this.dbUserService.getUserExchange(userId);
      return await this.noticedCoinService.addActiveCall(
        userId,
        args,
        exchange,
      );
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  }

  public async noticedCoins(userId: string) {
    try {
      const exchange = await this.dbUserService.getUserExchange(userId);

      const coins = await this.noticedCoinService.getNoticedCoins(
        userId,
        exchange,
      );
      const coinsWithCurrentPrice = [];

      if (Array.isArray(coins)) {
        for (const coin of coins) {
          const exchange = await this.dbUserService.getUserExchange(userId);
          const currentCoinPrice = await this.coinPriceService.dbGetCoinPrice(
            coin.symbol,
            exchange,
          );
          coinsWithCurrentPrice.push({
            timestamp: String(coin.timestamp),
            symbol: coin.symbol,
            buyPrice: coin.buyPrice,
            currentPrice: currentCoinPrice,
            isActiveCall: coin.isActiveCall,
          });
        }
      }

      return coinsWithCurrentPrice;
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  }

  public async account(userId: string) {
    try {
      return this.dbUserService.dbGetUserAccount(userId);
    } catch (e) {
      throw Error('User Settings cannot be fetched');
    }
  }

  public async exchangeKeys(
    args: GQL.IExchangeKeysOnUserQueryArguments,
    userId: string,
  ) {
    try {
      const exchange = await this.dbUserService.getUserExchange(userId);
      return await this.keyService.getApiKey(userId, args.exchange);
    } catch (e) {
      throw Error('Exchange Keys cannot be fetched');
    }
  }

  public async splitview(userId: string) {
    try {
      const user = await this.dbUserService.dbGetUserAccount(userId);
      if (user.splitview) {
        return user.splitview;
      }

      return [];
    } catch (e) {
      throw Error('Split View settings cannot be fetched');
    }
  }

  public async logs(args: GQL.ILogsOnUserQueryArguments) {
    return await this.logService.dbGetLogs(
      args.limit || 500,
      args.service,
      args.level,
    );
  }

  private async saveAccountSaldo(
    userId: string,
    apiKey: DbKeyDocument,
    exchange: string,
  ) {
    try {
      const args = { noSmallAmount: false };

      if (exchange == Exchange.BINANCE) {
        const accountInfo = await this.binanceAccountInfoService.getAccountInfo(
          apiKey,
          args,
        );
        await this.saldoService.saveUserSaldo(userId, accountInfo, exchange);
        await this.saldoUsdService.saveUserSaldoUsd(
          userId,
          accountInfo,
          exchange,
        );
      }

      if (exchange == Exchange.GATEIO) {
        const accountInfo = await this.gateioAccountInfoService.getAccountInfo(
          apiKey,
          args,
        );
        await this.saldoService.saveUserSaldo(userId, accountInfo, exchange);
        await this.saldoUsdService.saveUserSaldoUsd(
          userId,
          accountInfo,
          exchange,
        );
      }

      if (exchange == Exchange.BYBIT) {
        //   const accountInfo = await this.accountInfoService.getBybitAccountInfo(
        //     apiKey,
        //     args,
        //   );
        //   await this.saldoService.saveUserSaldoBybit(userId, accountInfo);
        //   await this.saldoUsdService.saveUserSaldoUsd(
        //     userId,
        //     accountInfo,
        //     Exchange.BINANCE,
        //   );
      }
    } catch (e) {
      throw e;
    }
  }
}

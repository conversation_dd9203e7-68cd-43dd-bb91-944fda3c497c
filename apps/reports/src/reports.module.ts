import { Module } from '@nestjs/common';
import { DatabaseModule } from '@app/database';
import { BinanceApiModule } from '@app/binance-api';
import { SendMailModule } from '@app/send-mail';
import { JobsService } from './jobs.service';
import { SendReportsService } from './send-reports/send-reports.service';
import { SaveSaldoService } from './save-saldo/save-saldo.service';
import { ScheduleModule } from '@nestjs/schedule';
import { GateioApiModule } from '@app/gateio-api';

@Module({
  imports: [
    DatabaseModule,
    BinanceApiModule,
    GateioApiModule,
    SendMailModule,
    ScheduleModule.forRoot(),
  ],
  controllers: [],
  providers: [JobsService, SendReportsService, SaveSaldoService],
})
export class ReportsModule {}

import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbKeyService } from '@app/database/db-key/db-key.service';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { DbBotOrderService } from '@app/database/db-bot-order/db-bot-order.service';
import { BinanceNewOrderService } from '@app/binance-api/binance-new-order/binance-new-order.service';
import { SendMailService } from '@app/send-mail';
import { serverUrl } from '../../../../utils/currentServer';
import { DbUserService } from '@app/database/db-user/db-user.service';
import { Exchange } from '../../../../types/exchanges';
import { GateioNewOrderService } from '@app/gateio-api/gateio-new-order/gateio-new-order.service';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';

@Injectable()
export class StoplossService {
  constructor(
    private logger: DbLogService,
    private apiKeyService: DbKeyService,
    private coinPriceService: DbCoinPriceService,
    private botOrderService: DbBotOrderService,
    private binanceNewOrderService: BinanceNewOrderService,
    private gateioNewOrderService: GateioNewOrderService,
    private sendMailService: SendMailService,
    private userService: DbUserService,
    private dbExchangeInfoService: DbExchangeInfoService,
  ) {}

  async stopLoss() {
    const matchedBots = await this.botOrderService.dbGetBotOrders(null, true);
    const slOrders = matchedBots.filter(
      (x) => x.type === 'BOT_STOP_LOSS' || x.type === 'AUTO_STOP_LOSS',
    );

    for (const slOrder of slOrders) {
      try {
        const {
          price: slPrice,
          amount,
          user: userId,
          symbol,
          response,
        } = slOrder;

        if (!response) {
          const exchange = await this.userService.getUserExchange(userId);
          const currentCoinPrice = await this.coinPriceService.dbGetCoinPrice(
            symbol,
            exchange,
          );

          if (currentCoinPrice <= Number(slPrice)) {
            const apiKey = await this.apiKeyService.getApiKey(userId, exchange);
            let orderResponse = null;

            switch (exchange) {
              case Exchange.BINANCE:
                orderResponse = await this.binanceNewOrderService.newOrder(
                  userId,
                  apiKey,
                  {
                    symbol,
                    side: 'SELL',
                    type: 'MARKET',
                    quantity: amount,
                  },
                );
                break;
              case Exchange.GATEIO:
                const exchangeInfo =
                  (await this.dbExchangeInfoService.getLatestExchangeInfo(
                    exchange,
                    {
                      symbol: symbol,
                    },
                  )) as any;

                orderResponse = await this.gateioNewOrderService.newOrder(
                  userId,
                  apiKey,
                  exchangeInfo,
                  currentCoinPrice,
                  {
                    symbol,
                    side: 'SELL',
                    type: 'MARKET',
                    quantity: amount,
                    quantityUsd: amount * currentCoinPrice,
                  },
                );
                break;
            }

            this.logger.logOrders(
              symbol,
              exchange,
              '-> Stop loss on ',
              currentCoinPrice,
              'USDT. Stop loss price was set to',
              Number(slPrice),
            );

            if (orderResponse.orderId) {
              await this.botOrderService.dbBotOrderResponse(
                slOrder,
                orderResponse,
              );

              await this.sendMailService.sendOrderMail(
                userId,
                'Moonbot ' + symbol + ' Stop Loss',
                'Stop Loss',
                {
                  'Asset:': `<a href="${serverUrl}/coin/${symbol}">${symbol}</a>`,
                  'SL Price:': `${slPrice}`,
                  'Sell Price:': `${currentCoinPrice}`,
                  'Amount:': `${amount}`,
                  'Timestamp:': `${new Date().toLocaleString('de-DE')}`,
                },
                true,
              );
            }
          }
        }
      } catch (e) {
        this.logger.errorOrders(e.message, e);
      }
    }
  }
}

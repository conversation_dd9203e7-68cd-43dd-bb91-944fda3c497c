import { Injectable } from '@nestjs/common';
import { DbMacdService } from '@app/database/db-macd/db-macd.service';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbCandlesticksService } from '@app/database/db-candlesticks/db-candlesticks.service';
import { last } from 'ramda';
import { sendChannelMessage } from '../../../../utils/notifications';
import { calcAORangeFromTimestampNoFilter } from '../../../../indicators/AO';

@Injectable()
export class MacdCalculationService {
  constructor(
    private exchangeInfoService: DbExchangeInfoService,
    private macdService: DbMacdService,
    private candlesticksService: DbCandlesticksService,
    private dblogService: DbLogService,
  ) {}

  async calcForSymbol(symbol: string, interval: string, exchange: string) {
    try {
      const candleSticks = await this.candlesticksService.dbGetLastCandles(
        symbol,
        interval,
        exchange,
        64,
      );
      const aoValues = calcAORangeFromTimestampNoFilter(candleSticks);
      const prevEntry = await this.macdService.findOne(
        symbol,
        interval,
        exchange,
      );

      const state = {
        symbol,
        state: prevEntry
          ? this.notifyOnChange(symbol, aoValues, prevEntry.values)
          : 0,
      };

      await this.macdService.updateMACD(
        symbol,
        exchange,
        interval,
        aoValues,
        prevEntry,
      );
      return state;
    } catch (e) {
      this.dblogService.errorBot(e);
    }
  }

  async calcMACDForAllSymbols(interval: string, exchange: string) {
    const sendMACDNotification = false;

    try {
      const usdtSymbols =
        await this.exchangeInfoService.getUsdtSymbols(exchange);
      const results = await Promise.all(
        usdtSymbols.map((symbol) =>
          this.calcForSymbol(symbol, interval, exchange),
        ),
      );

      const buys = results.filter((x) => x.state === 1).map((x) => x.symbol);
      const sells = results.filter((x) => x.state === 2).map((x) => x.symbol);

      if (sendMACDNotification) {
        if (buys.length > 0) {
          await sendChannelMessage(
            `BUY [${buys.join(',')}] MACD 2h crossing up`,
          );
        }
        if (sells.length > 0) {
          await sendChannelMessage(
            `SELL [${sells.join(',')}] MACD 2h crossing down`,
          );
        }
      }
    } catch (e) {
      this.dblogService.logBot(e);
    }
  }

  notifyOnChange(_symbol: string, values, prevValues) {
    const value: any = last(values);
    const prevValue: any = last(prevValues);

    if (value !== undefined && prevValue !== undefined) {
      if (value.signal > 0 && prevValue.signal < 0) {
        return 1;
      } else if (value.signal < 0 && prevValue.signal > 0) {
        return 2;
      }
    }
    return 3;
  }
}

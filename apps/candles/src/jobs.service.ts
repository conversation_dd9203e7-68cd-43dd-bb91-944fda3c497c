import { Injectable } from '@nestjs/common';
import { MacdCalculationService } from './macd-calculation/macd-calculation.service';
import { FetchCandlesService } from './fetch-candles/fetch-candles.service';
import { FetchExchangeInfoService } from './fetch-exchange-info/fetch-exchange-info.service';
import { <PERSON>ron } from '@nestjs/schedule';
import { Exchange } from '../../../types/exchanges';

/**
 *    *    *    *    *    *
 ┬    ┬    ┬    ┬    ┬    ┬
 │    │    │    │    │    │
 │    │    │    │    │    └ day of week (0 - 7) (0 or 7 is Sun)
 │    │    │    │    └───── month (1 - 12)
 │    │    │    └────────── day of month (1 - 31)
 │    │    └─────────────── hour (0 - 23)
 │    └──────────────────── minute (0 - 59)
 └───────────────────────── second (0 - 59, OPTIONAL)
 */

@Injectable()
export class JobsService {
  constructor(
    private fetchExchangeInfoService: FetchExchangeInfoService,
    private fetchCandlesService: FetchCandlesService,
    private macdCalculationService: MacdCalculationService,
  ) {}

  initialFetchDone = false;

  @Cron('40 40 */2 * * *')
  async fetchExchangeInfoBinance() {
    await this.fetchExchangeInfoService.fetchExchangeInfo(Exchange.BINANCE);
  }

  @Cron('40 40 */2 * * *')
  async fetchExchangeInfoGateio() {
    await this.fetchExchangeInfoService.fetchExchangeInfo(Exchange.GATEIO);
  }

  @Cron('2 */10 * * * *')
  async fetchAllCandles1() {
    if (this.initialFetchDone)
      await this.fetchCandlesService.fetchAllCandles1m(Exchange.BINANCE);
  }

  // @Cron('10 */5 * * * *')
  // async fetchAllCandles5() {
  //   if (this.initialFetchDone) {
  //     await this.fetchCandlesService.fetchAllCandles('5', 1, Exchange.BINANCE);
  //     await this.fetchCandlesService.fetchAllCandles('5', 1, Exchange.GATEIO);
  //   }
  // }

  @Cron('10 */5 * * * *')
  async fetchAllCandlesBinance15() {
    if (this.initialFetchDone) {
      // await this.fetchCandlesService.fetchAllCandles('15', 3, Exchange.BINANCE);
    }
  }

  @Cron('10 */15 * * * *')
  async fetchAllCandlesGateio15() {
    if (this.initialFetchDone) {
      await this.fetchCandlesService.fetchWhitelistedCandles(
        '15',
        3,
        Exchange.GATEIO,
      );
    }
  }

  @Cron('30 1 * * * *')
  async fetchAllCandles60() {
    if (this.initialFetchDone) {
      // await this.fetchCandlesService.fetchAllCandles('60', 3, Exchange.BINANCE);
    }
  }

  @Cron('30 1 * * * *')
  async fetchAllCandles60Gateio() {
    if (this.initialFetchDone) {
      // await this.fetchCandlesService.fetchAllCandles('60', 5, Exchange.GATEIO);
    }
  }

  @Cron('30 1 */2 * * *')
  async fetchAllCandles120Binance() {
    if (this.initialFetchDone) {
      await this.fetchCandlesService.fetchAllCandles(
        '120',
        3,
        Exchange.BINANCE,
      );
    }
  }

  @Cron('30 0 */2 * * *')
  async fetchAllCandles120GateioWhitelisted() {
    if (this.initialFetchDone) {
      // await this.fetchCandlesService.fetchWhitelistedCandles(
      //   '120',
      //   1,
      //   Exchange.GATEIO,
      // );
    }
  }

  @Cron('30 1 */2 * * *')
  async fetchAllCandles120Gateio() {
    if (this.initialFetchDone) {
      await this.fetchCandlesService.fetchAllCandles('120', 3, Exchange.GATEIO);
    }
  }

  @Cron('30 1 */4 * * *')
  async fetchAllCandles240Binance() {
    if (this.initialFetchDone) {
      // await this.fetchCandlesService.fetchAllCandles(
      //   '240',
      //   3,
      //   Exchange.BINANCE,
      // );
    }
  }

  @Cron('30 0 */4 * * *')
  async fetchAllCandles240GateioWhitelisted() {
    if (this.initialFetchDone) {
      // await this.fetchCandlesService.fetchWhitelistedCandles(
      //   '240',
      //   3,
      //   Exchange.GATEIO,
      // );
    }
  }

  @Cron('30 1 */4 * * *')
  async fetchAllCandles240Gateio() {
    if (this.initialFetchDone) {
      // await this.fetchCandlesService.fetchAllCandles('240', 3, Exchange.GATEIO);
    }
  }

  @Cron('30 1 */6 * * *')
  async fetchAllCandles1dBinance() {
    if (this.initialFetchDone) {
      // await this.fetchCandlesService.fetchAllCandles('1d', 3, Exchange.BINANCE);
    }
  }

  @Cron('30 0 */6 * * *')
  async fetchAllCandles1dGateioWhitelisted() {
    if (this.initialFetchDone) {
      // await this.fetchCandlesService.fetchWhitelistedCandles(
      //   '1d',
      //   3,
      //   Exchange.GATEIO,
      // );
    }
  }

  @Cron('30 1 */6 * * *')
  async fetchAllCandles1dGateio() {
    if (this.initialFetchDone) {
      // await this.fetchCandlesService.fetchAllCandles('1d', 3, Exchange.GATEIO);
    }
  }

  @Cron('45 */15 * * * *')
  async calcMACDForAllSymbolsBinance() {
    await this.macdCalculationService.calcMACDForAllSymbols(
      '15',
      Exchange.BINANCE,
    );
    await this.macdCalculationService.calcMACDForAllSymbols(
      '60',
      Exchange.BINANCE,
    );
    await this.macdCalculationService.calcMACDForAllSymbols(
      '120',
      Exchange.BINANCE,
    );
    await this.macdCalculationService.calcMACDForAllSymbols(
      '240',
      Exchange.BINANCE,
    );
    await this.macdCalculationService.calcMACDForAllSymbols(
      '1d',
      Exchange.BINANCE,
    );
  }

  @Cron('45 */15 * * * *')
  async calcMACDForAllSymbolsGateio() {
    await this.macdCalculationService.calcMACDForAllSymbols(
      '15',
      Exchange.GATEIO,
    );
    // await this.macdCalculationService.calcMACDForAllSymbols(
    //   '60',
    //   Exchange.GATEIO,
    // );
    await this.macdCalculationService.calcMACDForAllSymbols(
      '120',
      Exchange.GATEIO,
    );
    // await this.macdCalculationService.calcMACDForAllSymbols(
    //   '240',
    //   Exchange.GATEIO,
    // );
    // await this.macdCalculationService.calcMACDForAllSymbols(
    //   '1d',
    //   Exchange.GATEIO,
    // );
  }

  async startJobs() {
    // await this.fetchCandlesService.fetchWhitelistedCandles(
    //   '15',
    //   5,
    //   Exchange.GATEIO,
    // );
    await this.fetchExchangeInfoService.fetchExchangeInfo(Exchange.BINANCE);
    await this.fetchExchangeInfoService.fetchExchangeInfo(Exchange.GATEIO);
    this.initialFetchDone = false;

    const from = new Date();
    const prefetch = true;

    if (prefetch) {
      await this.fetchCandlesService.fetchHistoricalCandlesForWhitelistedSymbols(
        '15',
        new Date().getTime() - 1000 * 60 * 15,
        Exchange.GATEIO,
      );
      // await this.fetchCandlesService.fetchHistoricalCandlesForAllSymbols(
      //   '60',
      //   from,
      //   Exchange.BINANCE,
      // );
      //
      // await this.fetchCandlesService.fetchHistoricalCandlesForAllSymbols(
      //   '120',
      //   from,
      //   Exchange.GATEIO,
      // );
      //
      // await this.fetchCandlesService.fetchHistoricalCandlesForAllSymbols(
      //   '240',
      //   from,
      //   Exchange.GATEIO,
      // );
      //
      // await this.fetchCandlesService.fetchHistoricalCandlesForAllSymbols(
      //   '1d',
      //   from,
      //   Exchange.GATEIO,
      // );
    }

    //await this.fetchAllCandles120Gateio();
    // await this.fetchAllCandles240Gateio();
    //await this.fetchAllCandlesGateio15();

    // MACD
    await this.calcMACDForAllSymbolsGateio();
    //await this.macdCalculationService.calcMACDForAllSymbols('15');
    // await this.macdCalculationService.calcMACDForAllSymbols('60');
    // await this.macdCalculationService.calcMACDForAllSymbols('120');
    // await this.macdCalculationService.calcMACDForAllSymbols('240');
    // await this.macdCalculationService.calcMACDForAllSymbols('1d');

    this.initialFetchDone = true;
  }
}

import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import { BinanceExchangeInfoService } from '@app/binance-api/binance-exchange-info/binance-exchange-info.service';
import { Exchange } from '../../../../types/exchanges';
import { GateioExchangeInfoService } from '@app/gateio-api/gateio-exchange-info/gateio-exchange-info.service';

@Injectable()
export class FetchExchangeInfoService {
  constructor(
    private dblogService: DbLogService,
    private exchangeInfoService: DbExchangeInfoService,
    private binanceExchangeInfoService: BinanceExchangeInfoService,
    private gateioExchangeInfoService: GateioExchangeInfoService,
  ) {}

  async fetchExchangeInfo(exchange: string) {
    try {
      let exchangeInfo = null;

      switch (exchange) {
        case Exchange.GATEIO:
          exchangeInfo = await this.gateioExchangeInfoService.getExchangeInfo();
          break;

        case Exchange.BINANCE:
        default:
          exchangeInfo =
            await this.binanceExchangeInfoService.getExchangeInfo();
          break;
      }

      let i = 0;
      if (exchangeInfo && exchangeInfo.symbols) {
        await this.exchangeInfoService.removeAll(exchange);
        for (const symbol of exchangeInfo.symbols) {
          try {
            if (symbol.status === 'TRADING') {
              await this.exchangeInfoService.saveExchangeInfoForSymbol(
                symbol,
                exchange,
              );
              i++;
            }
          } catch (e) {
            this.dblogService.errorCandles(symbol, e);
          }
        }
      }

      this.dblogService.logServer(
        `Fetched ${exchange} exchange info for ` +
          exchangeInfo.symbols.length +
          ' symbols. Added ' +
          i +
          ' symbols.',
      );
    } catch (e) {
      this.dblogService.errorServer(e.message);
    }
  }
}

import { Injectable } from '@nestjs/common';
import { DbAutoBotService } from '@app/database/db-auto-bot/db-auto-bot.service';
import { DbWhiteListService } from '@app/database/db-white-list/db-white-list.service';
import { DbStrategyAllTestService } from '@app/database/db-strategy-all-test/db-strategy-all-test.service';
import { Strategies, StrategyIntervals } from '../../../../utils/Strategies';
import { AllStrategyConsumer } from './all-strategy.consumer';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import { DbUserService } from '@app/database/db-user/db-user.service';
import { uniq } from 'ramda';

@Injectable()
export class AllStrategyProducer {
  constructor(
    private autobotService: DbAutoBotService,
    private whiteListService: DbWhiteListService,
    private allTestService: DbStrategyAllTestService,
    private allStrategyConsumer: AllStrategyConsumer,
    private exchangeService: DbExchangeInfoService,
    private dbUserService: DbUserService,
  ) {}

  public async runAllStrategyTests(exchange: string) {
    const autobots = await this.autobotService.dbGetAllAutobots();

    const userIds = uniq(autobots.map((x) => x.userId));

    for (const userId of userIds) {
      await this.createNewStrategies(userId, exchange);
    }
  }

  private async createNewStrategies(userId: string, exchange: string) {
    const invest = 1000;
    const endDate = new Date();
    const startDate = new Date(2023, 0, 1);

    // Get whitelist
    const symbols: string[] = (
      await this.whiteListService.dbGetWhiteListCoins(userId, exchange)
    )
      .map((x) => x.symbol)
      .sort((a, b) => a.localeCompare(b));
    //
    // const symbols =
    //   await this.exchangeService.getUsdtSymbolsWithoutExcluded(exchange);

    for (const strategy of Strategies) {
      let intervals = StrategyIntervals;
      if (strategy == 'nesterov') {
        intervals = ['15', '60'];
      }

      for (const interval of intervals) {
        const newStrategy = await this.allTestService.dbCreateStrategyTest({
          userId: userId,
          interval,
          strategy,
          invest,
          symbols: symbols,
          startDate,
          endDate,
          exchange: exchange,
        });

        await this.allStrategyConsumer.processStrategy(newStrategy);
      }
    }
  }
}

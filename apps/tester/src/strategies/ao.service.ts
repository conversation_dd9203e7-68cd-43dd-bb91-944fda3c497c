import { Injectable } from '@nestjs/common';
import { DbStrategyTestDocument } from '@app/database/db-strategy-test/db-strategy-test.model';
import { TestExecutorService } from './test-executor.service';

@Injectable()
export class AoService {
  constructor(private testExecutorService: TestExecutorService) {}

  public async cross(strat: DbStrategyTestDocument) {
    const buyFunction = ({ third }) => third > 0;
    const sellFunction = ({ third }) => third < 0;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async swing(strat: DbStrategyTestDocument) {
    const buyFunction = ({ second, third }) => third > second;
    const sellFunction = ({ second, third }) => third < second;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async crossSwing(strat: DbStrategyTestDocument) {
    const buyFunction = ({ second, third }) => third > second && third > 0;
    const sellFunction = ({ second, third }) => third < second || third < 0;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async btcCross(strat: DbStrategyTestDocument) {
    const buyFunction = ({ btcThird }) => btcThird > 0;
    const sellFunction = ({ btcThird }) => btcThird < 0;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async btcSwing(strat: DbStrategyTestDocument) {
    const buyFunction = ({ btcSecond, btcThird }) => btcThird > btcSecond;
    const sellFunction = ({ btcSecond, btcThird }) => btcThird < btcSecond;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async btcCrossSwing(strat: DbStrategyTestDocument) {
    const buyFunction = ({ btcSecond, btcThird }) =>
      btcThird > btcSecond && btcThird > 0;
    const sellFunction = ({ btcSecond, btcThird }) =>
      btcThird < btcSecond || btcThird < 0;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async crossBtcSwing(strat: DbStrategyTestDocument) {
    const buyFunction = ({ third, btcSecond, btcThird }) =>
      third > 0 && btcThird > btcSecond;

    const sellFunction = ({ third, btcSecond, btcThird }) =>
      third < 0 || btcThird < btcSecond;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async swingBtcSwing(strat: DbStrategyTestDocument) {
    const buyFunction = ({ second, third, btcSecond, btcThird }) =>
      third > second && btcThird > btcSecond;

    const sellFunction = ({ second, third, btcSecond, btcThird }) =>
      third < second || btcThird < btcSecond;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async crossSwingBtcSwing(strat: DbStrategyTestDocument) {
    const buyFunction = ({ second, third, btcSecond, btcThird }) =>
      third > second && third > 0 && btcThird > btcSecond;

    const sellFunction = ({ second, third, btcSecond, btcThird }) =>
      third < second || third < 0 || btcThird < btcSecond;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async crossBtcCross(strat: DbStrategyTestDocument) {
    const buyFunction = ({ third, btcThird }) => third > 0 && btcThird > 0;

    const sellFunction = ({ third, btcThird }) => third < 0 || btcThird < 0;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async swingBtcCross(strat: DbStrategyTestDocument) {
    const buyFunction = ({ second, third, btcThird }) =>
      third > second && btcThird > 0;

    const sellFunction = ({ second, third, btcThird }) =>
      third < second || btcThird < 0;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async crossSwingBtcCross(strat: DbStrategyTestDocument) {
    const buyFunction = ({ second, third, btcThird }) =>
      third > second && third > 0 && btcThird > 0;

    const sellFunction = ({ second, third, btcThird }) =>
      third < second || third < 0 || btcThird < 0;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async emaAdx(strat: DbStrategyTestDocument) {
    const buyFunction = ({ ema25, ema100, ema200, adx, price }) =>
      price > ema200 && ema25 > ema100 && ema100 > ema200 && adx > 30;

    const sellFunction = ({ ema25, ema100, ema200, price }) =>
      price < ema200 && ema25 < ema100 && ema100 < ema200;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async ema25_50(strat: DbStrategyTestDocument) {
    const buyFunction = ({ ema25, ema50 }) => ema25 > ema50;

    const sellFunction = ({ ema25, ema50 }) => ema25 < ema50;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async ema50_100(strat: DbStrategyTestDocument) {
    const buyFunction = ({ ema100, ema50 }) => ema50 > ema100;

    const sellFunction = ({ ema100, ema50 }) => ema50 < ema100;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async ema100_200(strat: DbStrategyTestDocument) {
    const buyFunction = ({ ema100, ema200 }) => ema100 > ema200;

    const sellFunction = ({ ema100, ema200 }) => ema100 < ema200;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async rsi(strat: DbStrategyTestDocument) {
    const buyFunction = ({ rsi }) => rsi < 30;

    const sellFunction = ({ rsi }) => rsi > 70;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }

  public async emaAdxAoCross(strat: DbStrategyTestDocument) {
    const buyFunction = ({ ema25, ema100, ema200, adx, price, third }) =>
      price > ema200 &&
      ema25 > ema100 &&
      ema100 > ema200 &&
      adx > 30 &&
      third > 0;

    const sellFunction = ({ ema25, ema100, ema200, price, third }) =>
      (price < ema200 && ema25 < ema100 && ema100 < ema200) || third < 0;

    await this.testExecutorService.test(strat, buyFunction, sellFunction);
  }
}

import { equals, last } from 'ramda';
import { Injectable } from '@nestjs/common';
import { DbStrategyTestService } from '@app/database/db-strategy-test/db-strategy-test.service';
import { DbIndicatorService } from '@app/database/db-indicator/db-indicator.service';

@Injectable()
export class NeuralAdviceService {
  constructor(
    private dbStrategyTestService: DbStrategyTestService,
    private dbIndicatorService: DbIndicatorService,
  ) {}

  public botAdvice = async (
    advice: string,
    price: number,
    meanAlpha: number,
    settings,
    closeTime,
  ) => {
    const model = await this.dbStrategyTestService.findByTestId(
      settings.testId,
    );

    const newTrade: any = {
      tradeDate: closeTime,
      position: advice,
      price,
      predictedChange: meanAlpha,
      profit: null,
    };

    // Trades
    const botTrades: any = model.botTrades || [];
    let prevTrade = null;
    if (botTrades.length > 0) {
      prevTrade = last(botTrades);

      if (equals(advice, 'short')) {
        newTrade.profit = (price / prevTrade.price - 1) * 100;
      }
    }
    botTrades.push(newTrade);

    // Profit
    let totalProfit = 0;
    let totalProfitUsd = settings.invest;

    for (const trade of botTrades) {
      if (trade.profit) {
        totalProfit += trade.profit;
        totalProfitUsd = totalProfitUsd * ((100 + trade.profit) / 100);
      }
    }

    // RoundTrips
    const roundTrips = model.roundTrips || [];

    const updateIndicator = false;
    if (updateIndicator) {
      await this.dbIndicatorService.addIndicator({
        symbol: settings.symbol,
        indicator: settings.strategy,
        action: advice,
        price: price,
        timestamp: new Date(),
        candleTime: closeTime,
        interval: settings.interval,
        indicatorValue: meanAlpha,
        newAdded: false,
        profit: 0,
        exchange: settings.exchange,
      });
    }

    await this.dbStrategyTestService.updateTrade({
      strategyId: settings.testId,
      isBuy: advice == 'long',
      count: botTrades.length,
      endCount: botTrades.length,
      posTrade: newTrade.profit ? newTrade.profit > 0 : false,
      endUsd: totalProfitUsd,
      botTrades,
    });
  };
}

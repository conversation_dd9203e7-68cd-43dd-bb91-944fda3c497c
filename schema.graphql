schema {
    query: Query
    mutation: Mutation
}

type Query {
    session: Session
    exchange: ExchangeQuery
    indicators: IndicatorsQuery
    instruments: InstrumentsQuery
    strategy: StrategyQuery
    user: UserQuery
    bot: BotQuery
    pump: PumpQuery
}

type Mutation {
    # Login
    login(username: String!, password: String!): LoginResponse
    register(username: String!, password: String!, email: String!): Boolean
    logout: Boolean

    # Settings
    accountSettingsMutation(email: String, pumpbot: Boolean, alwaysSetStoploss: Boolean, alwaysSetTP: Boolean): UserAccount

    # Settings exchange
    saveExchangeMutation(exchange: String!): Common

    # Settings - API Key
    saveApiKeyMutation(key: String!, secret: String!, exchange: String!): Api<PERSON><PERSON>

    # Save split view
    saveSplitMutation(configs: [SplitViewSave]): Common

    # Saldo
    saveCurrentSaldo: Boolean

    # Order
    cancelOrderMutation(symbol: String!, orderId: String!, type: String!, recvWindow: Int): Common
    orderMutation(symbol: String!,
        side: String!,
        type: String!,
        quantity: Float!,
        price: Float,
        timeInForce: String,
        stopPrice: Float,
        icebergQty: Float,
        buyStopActive: Boolean,
        buyTPActive: Boolean,
        botTPPrice: Float,
        botStopPrice: Float,
        quantityUsd: Float,
        recvWindow: Int): NewOrder

    commentMutation(symbol: String!, comment: String): Comment

    # Bot
    addNewBotMutation(symbol: String
        interval: String
        strategy: String
        strategyParams: String
        amountUsdtInvested: Float
        simulation: Boolean
        startWithPosition: String
        longPrice: Float
        limitSell: Boolean): Common
    startStopBotMutation(id: String!, action: String!): Common
    transformToAutoBotMutation(id: String!): Common
    deleteBotMutation(id: String!): Common

    # Favorites
    saveWhiteListCoinMutation(symbol: String!): Common
    deleteWhiteListCoinMutation(symbol: String!): Common
    disableWhiteListCoinMutation(id: String!): Common
    buyWhiteListCoinMutation(symbol: String!): Common
    saveFavoriteCoinMutation(symbol: String!): FavoriteCoin
    addActiveCallMutation(symbol: String!): Common
    stopActiveCallMutation(symbol: String!): Common
    saveNoticedCoinMutation(symbol: String!, buyPrice: Float!): Common
    deleteNoticedCoinMutation(symbol: String!): Common

    # Strategy Tester
    strategyTestAdd(strategy: String!, interval: String!,startDate: String!,
        endDate: String!, symbol: String!, invest: Float, tp: Float, sl: Float): Common
    calcAllStrategy(strategy: String!): Common
    backtestRecalcMutation: Common

    # AutoBot
    createAutoBot(strategy: String!, usdPerCoin: Float!): Common
    modifyNumBotsMutation(id: String!, numbots: Int): Common
    autoUsdPerCoinMutation(botId: String!, value: Boolean): Common
    stopAutoBotMutation(botId: String!): Common
    startAutoBotMutation(botId: String!): Common
    removeAutoBotMutation(botId: String!): Common
    sellAllMutation(botId: String!): Common
    buyNowMutation(botId: String!): Common
    shortMarketBotMutation(symbol: String): Common
    sellInMinusMutation(botId: String!, value: Boolean!): Common
    onlyFromWhitelistMutation(botId: String!, value: Boolean!): Common
    btcOnlyMutation(botId: String!, value: Boolean!): Common
    enableBuyOnlyAOPlusMutation(botId: String!, value: Boolean!): Common
    toggleBtcAoCrossMutation(botId: String!, value: Boolean!): Common
    selectBotModeMutation(botId: String!, interval: String!, value: String!): Common
    selectBotRefillWhitelist(botId: String!, value: Boolean!): Common
    fillPortfolioAfterSellMutation(botId: String!, value: Boolean!): Common

    toggleTakeProfitMutation(botId: String!, value: Boolean!): Common
    toggleStopLossMutation(botId: String!, value: Boolean!): Common

    setAutobotTakeProfitMutation(botId: String!, value: Int!): Common
    setAutobotStopLossMutation(botId: String!, value: Int!): Common

}

type Subscription {
    marketHistory(symbol: String): [Trade]
    depth(symbol: String!): OrderBook
    allTickers(symbol: String, base: String): [WSTicker]
    coinPrices: [WSCoinPrices]
    coinPrice(symbols: [String]): [WSCoinPrices]
}

############### Account, Login, Session
type Session {
    id: String
    username: String
    email: String
}

############## Binance
type ExchangeQuery {
    accountInfo(noSmallAmount: Boolean): AccountInfo
    mytrades(symbol: String!, limit: Int, fromId: Float, recvWindow: Int): [MyTrades]
    openOrders(symbol: String!, withBinance: Boolean): [OpenOrder]
    dailyStats(symbol: String!): DailyStats
    lastCandle(symbol: String!, interval: String!): Candle
    candles(symbol: String!, strategy: String!, limit: Int!, interval: String): [Candle]
    exchangeInfo(base: String, symbol: String): [ExchangeSymbol]
    prices: [Price]
    comment(symbol: String!): Comment
    orderbook(symbol: String!, limit: Int): OrderBook
    lastOrders(symbol: String, limit: Int, status: String): [NewOrder]
    currentPrice(symbol: String!): Price
    symbolTicker(symbol: String!): WSTicker
    topVolumeCoins(number: Int!): [WSTicker]
}

type IndicatorsQuery {
    macd(symbol: String!, interval: String): MACD
    macds(symbol: String!, interval: String): [MACD]
}

type InstrumentsQuery {
    fetch(filters: InstrumentFilters): [Instrument]
}

############### User
type UserQuery {
    saldo(limit: Int): SaldoResponse
    saldoUsd(days: Int): SaldoResponse
    favorites: [FavoriteCoin]
    noticedCoins: [NoticedCoin]
    account: UserAccount
    exchangeKeys(exchange: String): ApiKey
    splitview: [SplitView]
    logs(limit: Int, service: String, level: String): [Log]
}

type BotQuery {
    formInfo: AddNewForm
    marketBots: [MarketBot]
    signals(symbol: String, strategy: String, days: Int, interval: String, limit: Int): [BotSignal]
    signalMarkets(symbol: String, strategy: String, interval: String): [BotSignal]
    profit(strategy: String, symbol: String, interval: String): BotProfit
    strategyProfits(strategy: String, symbol: String, days: Int, profitProgress: Boolean): [BotProfitPerDay]
    profitPerDayForStrategy(strategy: String, interval: String, days: Int): BotProfitPerDay
    profitPerMonth(strategy: String, interval: String, months: Int): BotProfitPerMonthResult
    botOrders: [OpenOrder]
    topCoins: [TopCoin]
    suggestList: [WhiteListSuggest]

    # Whitelist
    whitelist: [WhiteListCoin]


    # Indicators
    getIndicators(symbol: String, indicator: String, interval: String): [Indicator]
    getAllIndicators: [Indicator]

    # Autobot
    autobot: AutoBot

    # Autobot profit graph
    profitGraph: [AutobotProfitHistory]

    # Last order
    lastBotOrders: [MarketBotTrade]

    # Altcoin index
    fearAltIndex: FearAltIndex
}

type StrategyQuery {
    getStrategyTests: [StrategyTest]
    getAllStrategyTests: [GlobalStrategyTest]
}

input InstrumentFilters {
    interval: String
    ao: String
    rsi: String
    nesterov: String
}

type Instrument {
    symbol: String
    isWhitelisted: Boolean
    isPump: Boolean
    isRecommended: Boolean
    isBinanceRecommended: Boolean
    isNewListed: Boolean
    percent1h: Float
    percent4h: Float
    percent24h: Float
    percentVolume1h: Float
    percentVolume2h: Float
    percentVolume4h: Float
    percentVolume24h: Float
    rsi15: Float
    rsi60: Float
    rsi120: Float
    rsi240: Float
    rsi1d: Float
    listedOn: Date
    updateTimestamp: Date
    interval: String
    ao: Float
    adx: Float
    rsi: Float
    btcAO1d: Float
    atr: Float
    ema25: Float
    ema50: Float
    ema100: Float
    mfi: Float
    vwap: Float
    ichimoku: Any
    roc: Float
    bb_upper: Float
    bb_middle: Float
    bb_lower: Float
    bb_obv: Float
    ad: Float
    so_k: Float
    so_d: Float
    sma7: Float
    sma21: Float
    sma100: Float
    macd: Float
    price: Float
}

type Indicator {
    symbol: String
    interval: String
    indicator: String
    action: String
    price: Float
    timestamp: Date
}

type PumpQuery {
    lastPumps: [Pump]
}

type Pump {
    symbol: String
    notified: Boolean
    price: Float
    diff: Float
    timestamp: String
}

type LoginResponse {
    access_token: String
    username: String
    userId: String
}

type AccountInfo {
    makerCommission: Float
    takerCommission: Float
    buyerCommission: Float
    sellerCommission: Float
    canTrade: Boolean
    canWithdraw: Boolean
    canDeposit: Boolean
    balances(symbols: [String]): [Balance]
    btcValue: Float
    usdValue: Float
    accountType: String
    permissions: [String]
    updateTime: Float
}

type Balance {
    idx: String
    asset: String
    free: Float
    locked: Float
    usdValue: Float
    btcValue: Float
}

type MyTrades {
    id: Float
    orderId: String
    price: Float
    qty: Float
    commission: Float
    commissionUsd: Float
    commissionAsset: String
    time: Float
    isBuyer: Boolean
    isMaker: Boolean
    isBestMatch: Boolean

    trades: [MyTrade]
}

type MyTrade {
    id: Float
    orderId: String
    price: Float
    qty: Float
    commission: Float
    commissionUsd: Float
    commissionAsset: String
    time: Float
    isBuyer: Boolean
    isMaker: Boolean
    isBestMatch: Boolean
}

type OpenOrder {
    _id: String
    symbol: String
    orderId: String
    clientOrderId: String
    price: Float
    origQty: Float
    executedQty: Float
    status: String
    timeInForce: String
    type: String
    side: String
    stopPrice: Float
    icebergQty: Float
    time: Float
    isWorking: Boolean
}

type DailyStats {
    priceChange: Float
    priceChangePercent: Float
    weightedAvgPrice: Float
    prevClosePrice: Float
    lastPrice: Float
    lastQty: Float
    bidPrice: Float
    bidQty: Float
    askPrice: Float
    askQty: Float
    openPrice: Float
    highPrice: Float
    lowPrice: Float
    volume: Float
    quoteVolume: Float
    openTime: Float
    closeTime: Float
    firstId: Float
    lastId: Float
    count: Int
}

type ExchangeSymbol {
    symbol: String
    baseAsset: String
    baseAssetPrecision: Int
    quoteAsset: String
    quotePrecision: Float
    orderTypes: [String]
    icebergAllowed: Boolean
    filters: [Filter]
}

type Filter {
    filterType: String
    minPrice: Float
    maxPrice: Float
    tickSize: Float
    minQty: Float
    maxQty: Float
    stepSize: Float
    minNotional: Float
}

type SaldoResponse {
    saldos: [Saldo]
    yesterday: Saldo
}

type Saldo {
    timestamp: String
    usd: Float
    btc: Float
    usdDiff: Float
}

type NewOrder {
    symbol: String
    orderId: String
    clientOrderId: String
    transactTime: Float
    price: Float
    origQty: Float
    executedQty: Float
    status: String
    timeInForce: String
    type: String
    side: String
    ok: String
    error: String
}

type FavoriteCoin {
    symbol: String
}

type WhiteListCoin {
    id: String
    symbol: String
    timestamp: String
    autoAdded: Boolean
    indicator: Indicator
    disabled: Boolean
}

type NoticedCoin {
    symbol: String
    buyPrice: Float
    currentPrice: Float
    timestamp: String
    isActiveCall: Boolean
}

type Trade {
    rowId: String
    id: Float
    price: Float
    qty: Float
    time: Float
    isBuyerMaker: Boolean
    isBestMatch: Boolean
    symbol: String
    maker: Boolean
    tradeId: Float
    eventType: String
    tradeTime: Float
}

type OrderBook {
    lastUpdateId: Float
    asks: [DepthDetail]
    bids: [DepthDetail]
    error: Float
}

type DepthDetail {
    rowId: String
    price: Float
    quantity: Float
}

type WSTicker {
    eventType: String
    eventTime: Float
    symbol: String
    priceChange: Float
    priceChangePercent: Float
    weightedAvg: Float
    prevDayClose: Float
    curDayClose: Float
    bestBid: Float
    bestBidQnt: Float
    bestAsk: Float
    bestAskQnt: Float
    open: Float
    high: Float
    low: Float
    volume: Float
    volumeQuote: Float
    openTime: Float
    closeTime: Float
    firstTradeId: Float
    lastTradeId: Float
    totalTrades: Float
    percent4h: Float
    percentVolume1h: Float
    percentVolume2h: Float
    percentVolume4h: Float
    percentVolume24h: Float
    advice: String
    volumeQuoteBTC: Float
}

type WSCoinPrices {
    symbol: String
    price: Float
}

type UserAccount {
    username: String
    email: String
    notifications: UserNotifications
    splitview: [SplitView]
    trade: UserTrade
    exchange: String
}

type UserNotifications {
    pumpbot: Boolean
    signals: Any
    marketBots: Boolean
}

type UserTrade {
    alwaysSetStoploss: Boolean
    alwaysSetTP: Boolean
}

type ApiKey {
    key: String
    secret: String
    exchange: String
    error: String
    activated: Boolean
}

type Common {
    ok: Boolean
    message: String
    error: String
}

type Comment {
    symbol: String
    userId: String
    comment: String
    ok: String
    error: String
}

type NewBot {
    symbol: String
    interval: String
    strategy: String
    strategyParams: String
    amountUsdtInvested: Float
    simulation: Boolean
    startWithPosition: String
    longPrice: Float
    limitSell: Boolean
}

type Price {
    symbol: String
    price: Float
}

type BotProfit {
    profit: Float
    tradesPos: Int
    tradesNeg: Int
}

type BotProfitPerDay {
    strategy: String
    interval: String
    totalProfit: Float
    currentDiff: Float
    tradesPos: Int
    tradesNeg: Int
    dailyProfits: [BotProfits]
}

type BotProfitPerMonthResult {
    strategy: String
    interval: String
    totalProfit: Float
    tradesPos: Int
    tradesNeg: Int
    monthlyProfits: [BotProfitsForMonth]
}

type BotProfitsForMonth {
    month: String
    monthStr: String
    profit: Float
}

type BotProfits {
    day: String
    dateStr: String
    profit: Float
}

type BotSignal {
    id: String
    symbol: String
    price: Float
    advice: String
    strategy: String
    interval: String
    profit: Float
    timestamp: Date
    currentDiff: Float
    candleTime: Date
}

type MarketBot {
    id: String
    symbol: String
    exchange: String
    strategy: String
    amountUsdtInvested: Float
    startBalanceUSD: Float
    finalBalanceUSD: Float
    startBalanceBTC: Float
    finalBalanceBTC: Float
    startPrice: Float
    endPrice: Float
    marketPercent: Float
    startDate: Date
    stopDate: Date
    updatedOn: Date
    strategyParams: String
    interval: String
    profit: Float
    trades: [BotTrade]
    lastTrade: String
    active: Boolean
    position: String
    sellInMinus: Boolean
    simulation: Boolean
    roundTrips: [BotRoundTrip]
    timestamp: String
    fromAutoBot: Boolean
    currentProfit: Float
    currentValueUsd: Float
    investedUsd: Float
}

type BotTrade {
    tradeDate: String
    position: String
    price: Float
    profit: Float
    profitWithoutFees: Float
    tradeResult: String
}

type BotRoundTrip {
    entryDate: Date
    exitDate: Date
    entryPrice: Float
    exitPrice: Float
    duration: Float
    profit: Float
    profitWithoutFees: Float
}

type AddNewForm {
    strategies: [BotStrategy]
}

type BotStrategy {
    name: String
    description: String
    interval: String
    defaultParams: String
}

type MACD {
    signal: Float
    openTime: Float
    MACD: Float
    histogram: Float
    coinPriceUsd: Float
}

type BotOrder {
    _id: String
    symbol: String
    type: String
    amount: String
    price: Float
    markForTP: Boolean
    tpPrice: Float
    addTimestamp: String
    executionTimestamp: String
    status: String
}

type TopCoin {
    symbol: String
    profit: Float
    strategy: String
}

type StrategyTest {
    symbol: String
    interval: String
    startDate: String
    endDate: String
    strategy: String
    endUsd: Float
    invest: Float

    status: String
    profit: Float
    roundTrips: [BotRoundTrip]
    botTrades: [BotTrade]
    createTimestamp: String
}

type GlobalStrategyTest {
    strategy: String
    interval: String

    profit: Float
    profitUsd: Float
    numShorts: Int
    startDate: String
    endDate: String
    status: String
    endUsd: Float
    invest: Float

    currentSymbol: String
    progressStrategy: Int
    progressInterval: Int
    progressSymbol: Int

    investPerCoin: Float

    bestSymbolProfit: Float
    worstSymbolProfit: Float

    numTrades: Int
    posTrades: Int
    negTrades: Int

    bestPerformer: String
    worstPerformer: String

    avgProfit: Float
    avgHoldMinutes: Float
    avgTradesDay: Float
    bestTrade: Float
    avgTrade: Float
    worstTrade: Float
    createTimestamp: String
    updateTimestamp: String

    sl: Int
}

type AutoBot {
    id: String
    _id: String
    strategy: String
    interval: String
    usdPerCoin: String
    numbots: Int
    autoUsdPerCoin: Boolean
    marketBotsBig: [MarketBot]
    profitToday: Float
    profitTodayRelative: Float
    profitYesterday: Float
    profitThisWeek: Float
    profitThisMonth: Float
    profitLastMonth: Float
    profitOverall: Float
    active: Boolean
    archived: Boolean
    createTimestamp: Date
    lastTradeTimestamp: Date
    startTime: Date

    profitTodayUsd: Float
    profitYesterdayUsd: Float
    profitThisWeekUsd: Float
    profitThisMonthUsd: Float
    profitLastMonthUsd: Float
    profitOverallUsd: Float

    accumulatedInvestUsd: Float
    accumulatedValueUsd: Float
    accumulatedProfit: Float
    accumulatedProfitRelative: Float
    activeCount: Int

    tradesToday: Int
    tradesTodayShort: Int
    tradesTodayLong: Int

    successRateToday: Float
    winsToday: Int
    losesToday: Int

    autoRefillWhitelist: Boolean

    stoplossPercent: Int
    takeProfitPercent: Int
    enableTakeProfit: Boolean
    enableStoploss: Boolean
    enableSellInMinus: Boolean
    buyOnlyFromWhitelist: Boolean
    fillPortfolioAfterSell: Boolean
    buyOnlyOnAOPlus: Boolean
    useBtcAOCross: Boolean
    btcOnly: Boolean
}

type Candle {
    symbol: String
    interval: String
    timestamp: Date

    openTime: Float
    open: Float
    high: Float
    low: Float
    close: Float
    volume: Float
    closeTime: Float
    quoteVolume: Float
    trades: Float
    baseAssetVolume: Float
    quoteAssetVolume: Float
}

type SplitView {
    window: Int!
    market: String
    interval: String
}

input SplitViewSave {
    window: Int!
    market: String
    interval: String
}

type AutobotProfitHistory {
    percentHistory: Float
    usdHistory: Float
    timestamp: Date
}

type MarketBotTrade {
    id: String
    autobotId: String
    marketBotId: String
    asset: String
    position: String
    price: Float
    valueInUsd: Float
    profit: Float
    amount: Float
    tradeDate: Date
    btcPrice: Float
    profitUsd: Float
}

type WhiteListSuggest {
    symbol: String
    totalCount: Int
    neuralCount: Int
    longCount: Int
    shortCount: Int
}

type Log {
    msg: String
    service: String
    level: String
    timestamp: Date
}

type FearAltIndex {
    fearIndex: Int
    altIndex: Int
    fearText: String
    altText: String
}

scalar Any
scalar Date
import { Symbol } from 'binance-api-node';

export default class PriceCalculation {
  public static getFixedAmount(amount: number, symbol: Symbol): number {
    const filters = symbol.filters;
    const lotSize: any = filters.find((x) => x.filterType == 'LOT_SIZE') || {};
    const stepSize: any = lotSize.stepSize;
    let toFixed = -Math.floor(Math.log(stepSize) / Math.log(10) - 1) - 1;
    toFixed = toFixed < 0 ? 0 : toFixed;
    return Math.trunc(amount * Math.pow(10, toFixed)) / Math.pow(10, toFixed);
  }

  public static getFixedPrice(
    price: number,
    symbols: Symbol[],
    symbol: string,
  ): number {
    if (symbols && symbols.length > 0) {
      const symbolData: any = symbols.find((x) => x.symbol == symbol) || {};

      if (!symbolData.filters) {
        return price;
      }

      const filters = symbolData.filters;
      const priceFilter: any =
        filters.find((x) => x.filterType == 'PRICE_FILTER') || {};
      const tickSize = priceFilter.tickSize;
      let toFixed = -Math.floor(Math.log(tickSize) / Math.log(10) - 1) - 1;
      const priceFloat = Number(price);
      toFixed = toFixed < 0 ? 0 : toFixed;
      return (
        Math.trunc(priceFloat * Math.pow(10, toFixed)) / Math.pow(10, toFixed)
      );
    }

    return price;
  }

  /**
   * Prev: 100
   * Now: 200
   *
   * => 100
   *
   * @param prev
   * @param now
   */
  static toPercentGain(prev: number, now: number): number {
    return Number(((now / prev - 1) * 100).toFixed(2));
  }

  /**
   * Prev: 100
   * Now: 200
   * => 200
   *
   * @param prev
   * @param now
   */
  public static toSimplePercent(prev: number, now: number) {
    const result = ((now / prev) * 100).toFixed(2);

    if (Number(result) > 0) {
      return '+' + result;
    }

    return result;
  }

  static toVolume(x: number) {
    return x.toFixed(0);
  }

  static toClassName(x: number) {
    if (x > 0) {
      return 'text-success';
    } else if (x < 0) {
      return 'text-danger';
    } else {
      return 'text-muted';
    }
  }
}

import { Module } from '@nestjs/common';
import { DatabaseModule } from '@app/database';
import { BinanceCandleSticksService } from '@app/binance-api/binance-candle-sticks/binance-candle-sticks.service';
import { BinanceAccountInfoService } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { BinanceCancelOpenOrderService } from '@app/binance-api/binance-cancel-open-order/binance-cancel-open-order.service';
import { BinanceConnectService } from '@app/binance-api/binance-connect/binance-connect.service';
import { BinanceDailyStatsService } from '@app/binance-api/binance-daily-stats/binance-daily-stats.service';
import { BinanceExchangeInfoService } from '@app/binance-api/binance-exchange-info/binance-exchange-info.service';
import { BinanceMyTradesService } from '@app/binance-api/binance-my-trades/binance-my-trades.service';
import { BinanceNewOrderService } from '@app/binance-api/binance-new-order/binance-new-order.service';
import { BinanceOpenOrdersService } from '@app/binance-api/binance-open-orders/binance-open-orders.service';
import { BinanceOrderBookService } from '@app/binance-api/binance-order-book/binance-order-book.service';
import { BinancePricesService } from '@app/binance-api/binance-prices/binance-prices.service';
import { BinanceTradesService } from '@app/binance-api/binance-trades/binance-trades.service';
import { BinanceWsDepthService } from '@app/binance-api/binance-api/ws/binance-ws-depth.service';
import { BinanceWsUserService } from '@app/binance-api/binance-api/ws/binance-ws-user.service';
import { BinanceWsTradesService } from '@app/binance-api/binance-api/ws/binance-ws-trades.service';
import { BinanceWsAlltickersService } from '@app/binance-api/binance-api/ws/binance-ws-alltickers.service';

@Module({
  providers: [
    BinanceCandleSticksService,
    BinanceAccountInfoService,
    BinanceCancelOpenOrderService,
    BinanceConnectService,
    BinanceDailyStatsService,
    BinanceExchangeInfoService,
    BinanceMyTradesService,
    BinanceNewOrderService,
    BinanceOpenOrdersService,
    BinanceOrderBookService,
    BinancePricesService,
    BinanceTradesService,
    BinanceWsUserService,
    BinanceWsTradesService,
    BinanceWsDepthService,
    BinanceWsAlltickersService,
  ],
  exports: [
    BinanceCandleSticksService,
    BinanceAccountInfoService,
    BinanceCancelOpenOrderService,
    BinanceConnectService,
    BinanceDailyStatsService,
    BinanceExchangeInfoService,
    BinanceMyTradesService,
    BinanceNewOrderService,
    BinanceOpenOrdersService,
    BinanceOrderBookService,
    BinancePricesService,
    BinanceTradesService,
    BinanceWsUserService,
    BinanceWsTradesService,
    BinanceWsDepthService,
    BinanceWsAlltickersService,
  ],
  imports: [DatabaseModule],
})
export class BinanceApiModule {}

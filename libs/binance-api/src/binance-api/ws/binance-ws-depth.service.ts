import { BinanceConnectService } from '@app/binance-api/binance-connect/binance-connect.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class BinanceWsDepthService {
  constructor(private connectService: BinanceConnectService) {}

  depth(symbols: { symbol: string; level: number }[], callback: any) {
    const client = this.connectService.init();

    client.ws.partialDepth(symbols, callback);
  }
}

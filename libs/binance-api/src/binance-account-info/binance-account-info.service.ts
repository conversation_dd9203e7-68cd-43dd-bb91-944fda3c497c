import { Injectable } from '@nestjs/common';
import { v4 } from 'uuid';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbKeyService } from '@app/database/db-key/db-key.service';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { DbHiddenSymbolsService } from '@app/database/db-hidden-symbols/db-hidden-symbols.service';
import { BinanceConnectService } from '@app/binance-api/binance-connect/binance-connect.service';
import { DbKey } from '@app/database/db-key/db-key.model';
import { Account, AssetBalance } from 'binance-api-node';
import { Exchange } from '../../../../types/exchanges';

interface MbAssetBalance extends AssetBalance {
  usdValue: number;
  btcValue: number;
}

export interface MbAccountInfo extends Account {
  usdValue: number;
  btcValue: number;
  balances: MbAssetBalance[];
}

@Injectable()
export class BinanceAccountInfoService {
  constructor(
    private logger: DbLogService,
    private keyService: DbKeyService,
    private coinPriceService: DbCoinPriceService,
    private hiddenSymbolsService: DbHiddenSymbolsService,
    private binanceConnectService: BinanceConnectService,
  ) {}

  SMALLEST_AMOUNT = 2;

  public async getAccountInfo(
    apiKey: DbKey,
    args: {
      noSmallAmount: boolean;
      symbols?: string[];
    },
  ): Promise<MbAccountInfo> {
    const client = this.binanceConnectService.init(apiKey.key, apiKey.secret);

    try {
      const accountInfo = await client.accountInfo({
        useServerTime: true,
        recvWindow: 50000,
      } as any);

      return this.addBtcBalances(accountInfo as MbAccountInfo, args);
    } catch (e) {
      this.logger.errorServer(e);
    }

    return null;
  }

  async getAccountInfoWithRetry(userId: string) {
    const binanceKeys = await this.keyService.getApiKey(
      userId,
      Exchange.BINANCE,
    );
    let accountInfo = await this.getAccountInfo(binanceKeys, {
      noSmallAmount: false,
    });
    let i = 0;

    while (!accountInfo && i < 10) {
      i++;

      this.logger.logServer(
        Exchange.BINANCE,
        'Account info fetch failed. Retry ' + i,
      );

      accountInfo = await this.getAccountInfo(binanceKeys, {
        noSmallAmount: false,
      });

      if (accountInfo) {
        return accountInfo;
      }
    }

    return accountInfo;
  }

  async addBtcBalances(
    result: MbAccountInfo,
    args: {
      noSmallAmount: boolean;
      symbols?: string[];
    },
  ): Promise<MbAccountInfo> {
    const newResult = { ...result };

    newResult.usdValue = 0;
    newResult.btcValue = 0;

    const btcPrice = await this.coinPriceService.dbGetCoinPrice(
      'BTCUSDT',
      Exchange.BINANCE,
    );
    const balances = [];

    const hiddenSymbols = await this.hiddenSymbolsService.dbGetHiddenSymbols();
    const coinPrices = await this.coinPriceService.dbGetCoinPrices(
      Exchange.BINANCE,
    );

    newResult.balances.forEach((obj) => {
      const coin = { ...obj };
      const free = Number(coin.free);
      const locked = Number(coin.locked);
      const { asset } = coin;

      let usdValue = 0;
      let btcValue = 0;

      // only show items with balances
      if (free === 0 && locked === 0) {
        return;
      }

      if (asset === 'BTC') {
        btcValue = free + locked;
        usdValue = btcValue * btcPrice;
      } else if (asset === 'USDT') {
        usdValue = free + locked;
        btcValue = usdValue / btcPrice;
      } else {
        const coinPrice = coinPrices.find((x) => x.symbol == `${asset}USDT`);
        if (!coinPrice) {
          return;
        }

        btcValue = Number(((free + locked) * coinPrice.price) / btcPrice);
        usdValue = Number((free + locked) * coinPrice.price);

        if (args.noSmallAmount) {
          if (Number(usdValue) < Number(this.SMALLEST_AMOUNT)) {
            return;
          }
        }
      }

      if (Number.isNaN(btcValue)) {
        btcValue = 0;
      }
      if (Number.isNaN(usdValue)) {
        usdValue = 0;
      }

      newResult.usdValue += usdValue;
      newResult.btcValue += btcValue;

      if (!hiddenSymbols.includes(asset)) {
        if (!args.symbols || (args.symbols && args.symbols.includes(asset))) {
          balances.push({
            asset,
            free,
            locked,
            usdValue,
            btcValue,
            idx: v4(),
          });
        }
      }
    });

    newResult.balances = balances;

    return newResult;
  }
}

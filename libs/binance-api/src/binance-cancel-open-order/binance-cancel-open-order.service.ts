import { Injectable } from '@nestjs/common';
import { BinanceConnectService } from '@app/binance-api/binance-connect/binance-connect.service';

@Injectable()
export class BinanceCancelOpenOrderService {
  constructor(private connectService: BinanceConnectService) {}

  async cancelOpenOrder(apiKey, args: any) {
    const client = this.connectService.init(apiKey.key, apiKey.secret);

    return client.cancelOrder({
      symbol: args.symbol,
      useServerTime: true,
      orderId: parseInt(args.orderId, 10),
    });
  }
}

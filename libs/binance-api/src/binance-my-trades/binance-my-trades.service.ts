import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { BinanceConnectService } from '@app/binance-api/binance-connect/binance-connect.service';
import { Db<PERSON><PERSON> } from '@app/database/db-key/db-key.model';

@Injectable()
export class BinanceMyTradesService {
  constructor(
    private logger: DbLogService,
    private connect: BinanceConnectService,
  ) {}

  async getMyTrades(
    apiKey: DbKey,
    args: GQL.IMytradesOnExchangeQueryArguments,
  ) {
    const client = this.connect.init(apiKey.key, apiKey.secret);

    if (!(args.symbol === 'BTCBTC') && !(args.symbol === 'USDTBTC')) {
      try {
        return await client.myTrades(args);
      } catch (e) {
        throw e;
      }
    }

    return null;
  }
}

import { Injectable } from '@nestjs/common';
import { DbSaldoService } from '@app/database/db-saldo/db-saldo.service';
import { DbSaldoUsdService } from '@app/database/db-saldo-usd/db-saldo-usd.service';
import { DbBotOrderService } from '@app/database/db-bot-order/db-bot-order.service';
import { DbTradeService } from '@app/database/db-trade/db-trade.service';
import { BinanceConnectService } from '@app/binance-api/binance-connect/binance-connect.service';
import { BinanceAccountInfoService } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { DbKey } from '@app/database/db-key/db-key.model';
import {
  NewOrderLimit,
  NewOrderMarketBase,
  NewOrderSL,
  Order,
} from 'binance-api-node';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { MBOrderType } from '../../../../types/binance.types';
import { Exchange } from '../../../../types/exchanges';

@Injectable()
export class BinanceNewOrderService {
  constructor(
    private saldoService: DbSaldoService,
    private saldoUsdService: DbSaldoUsdService,
    private botOrderService: DbBotOrderService,
    private tradeService: DbTradeService,
    private binanceConnectService: BinanceConnectService,
    private binanceAccountInfoService: BinanceAccountInfoService,
    private logger: DbLogService,
  ) {}

  public async newOrder(
    userId: string,
    apiKey: DbKey,
    args: GQL.IOrderMutationOnMutationArguments,
  ) {
    const client = this.binanceConnectService.init(apiKey.key, apiKey.secret);

    try {
      let result: Order | undefined;

      const defaultProps = {
        recvWindow: args.recvWindow ? args.recvWindow : 10000,
      };

      switch (args.type) {
        case 'LIMIT':
          result = await client.order({
            symbol: args.symbol,
            side: args.side,
            type: 'LIMIT',
            quantity: String(args.quantity),
            price: Number(args.price).toFixed(8),
            timeInForce: args.timeInForce,
            ...defaultProps,
          } as NewOrderLimit);
          break;

        case 'MARKET':
          result = await client.order({
            symbol: args.symbol,
            side: args.side,
            type: 'MARKET',
            quantity: String(args.quantity),
            ...defaultProps,
          } as NewOrderMarketBase);
          break;

        case 'STOP_LOSS_LIMIT':
          result = await client.order({
            symbol: args.symbol,
            side: args.side,
            type: 'STOP_LOSS_LIMIT',
            quantity: String(args.quantity),
            price: Number(args.price).toFixed(8),
            timeInForce: args.timeInForce,
            stopPrice: args.stopPrice
              ? Number(args.stopPrice).toString(8)
              : undefined,
            ...defaultProps,
          } as NewOrderSL);
          break;

        default:
          return;
      }

      await this.saveUserSaldo(args, apiKey, result, userId);

      if (
        args.side === 'BUY' &&
        (result.status === 'FILLED' || result.status === 'PARTIALLY_FILLED')
      ) {
        await this.addAutoStopLossOrder(args, userId);
        await this.addAutoTakeProfitOrder(args, userId);
      }

      if (
        args.side === 'SELL' &&
        (result.status === 'FILLED' || result.status === 'PARTIALLY_FILLED')
      ) {
        await this.removeAutoSLTPOrders(args, userId);
      }

      return result;
    } catch (e) {
      this.logger.errorServer(e);
      throw e;
    }
  }

  private async addAutoTakeProfitOrder(
    args: GQL.IOrderMutationOnMutationArguments,
    userId: string,
  ) {
    if (args.buyTPActive) {
      await this.botOrderService.dbAddBotOrder(userId, Exchange.BINANCE, {
        ...args,
        type: MBOrderType.AUTO_TAKE_PROFIT,
        price: args.botTPPrice,
      });
    }
  }

  private async removeAutoSLTPOrders(
    args: GQL.IOrderMutationOnMutationArguments,
    userId: string,
  ) {
    const typesToRemove = [
      MBOrderType.AUTO_TAKE_PROFIT,
      MBOrderType.AUTO_STOP_LOSS,
      MBOrderType.BOT_TAKE_PROFIT,
      MBOrderType.BOT_STOP_LOSS,
    ];

    try {
      const result = await this.botOrderService.dbGetBotOrders(
        userId,
        false,
        Exchange.BINANCE,
      );

      for (const order of result) {
        if (order.symbol === args.symbol) {
          await this.botOrderService.dbDeleteBotOrder(
            userId,
            order._id.toString(),
          );
        }
      }
    } catch (e) {
      this.logger.errorServer(e);
    }
  }

  private async addAutoStopLossOrder(
    args: GQL.IOrderMutationOnMutationArguments,
    userId: string,
  ) {
    if (args.buyStopActive) {
      await this.botOrderService.dbAddBotOrder(userId, Exchange.BINANCE, {
        ...args,
        type: MBOrderType.AUTO_STOP_LOSS,
        price: args.botStopPrice,
      });
    }
  }

  private async saveUserSaldo(
    args: GQL.IOrderMutationOnMutationArguments,
    apiKey: DbKey,
    result: Order,
    userId: string,
  ) {
    const argsModified = { ...args, noSmallAmount: false };
    const accountInfo = await this.binanceAccountInfoService.getAccountInfo(
      apiKey,
      argsModified,
    );

    if (result.status === 'FILLED') {
      await this.saldoService.saveUserSaldo(
        userId,
        accountInfo,
        Exchange.BINANCE,
      );
      await this.saldoUsdService.saveUserSaldoUsd(
        userId,
        accountInfo,
        Exchange.BINANCE,
      );
    }

    await this.tradeService.saveUserTrade(
      userId,
      argsModified,
      result,
      Exchange.BINANCE,
    );
  }

  async newBotOrder(userId: string, args: any) {
    const result = await this.botOrderService.dbAddBotOrder(
      userId,
      Exchange.BINANCE,
      args,
    );

    if (args.buyStopActive) {
      await this.botOrderService.dbAddBotOrder(userId, Exchange.BINANCE, {
        ...args,
        type: 'AUTO_STOP_LOSS',
        price: args.botStopPrice,
      });
    }

    if (args.buyTPActive) {
      await this.botOrderService.dbAddBotOrder(userId, Exchange.BINANCE, {
        ...args,
        type: 'AUTO_TAKE_PROFIT',
        price: args.botTPPrice,
      });
    }

    return {
      symbol: result.symbol,
      orderId: result._id.toString(),
      transactTime: new Date(result.addTimestamp).getTime(),
      price: result.price,
      origQty: result.amount,
      executedQty: null,
      status: 'NEW',
      type: result.type,
      ok: true,
      clientOrderId: null,
      side: 'SELL',
      timeInForce: null,
      fills: null,
      icebergQty: null,
      isIsolated: null,
      isWorking: null,
      updateTime: new Date(),
      time: new Date(),
      orderListId: null,
      stopPrice: args.price,
      cummulativeQuoteQty: null,
    } as any;
  }
}

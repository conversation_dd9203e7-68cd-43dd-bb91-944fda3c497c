import PriceCalculation from '../../../../utils/PriceCalculation';
import { serverUrl } from '../../../../utils/currentServer';

export const createDailyReportText = async (content: any) => {
  // console.log(content);
  //
  const accountInfo: any = content.accountInfo;
  const userSaldo = content.userSaldo;

  const usdPercent: any = PriceCalculation.toPercentGain(
    userSaldo.yesterday?.usd,
    accountInfo.usdValue,
  );
  const btcPercent: any = PriceCalculation.toPercentGain(
    userSaldo.yesterday?.btc,
    accountInfo.btcValue,
  );

  const date = new Date().toLocaleDateString();
  let totalProfit = 0;
  let totalProfitRelative = 0;
  let totalProfitUsd = 0;
  let positiveTrades = 0;
  let negativeTrades = 0;
  let shortTrades = 0;
  let longTrades = 0;
  const trades = [];

  // tslint:disable-next-line:prefer-for-of
  for (let i = 0; i < content.trades.length; i++) {
    const trade = content.trades[i];
    let tradeProfitUsd = 0;
    let tradeProfit = 0;

    if (trade.position === 'short' && trade.profit != null) {
      tradeProfit = trade.profit;
      tradeProfitUsd = trade.profitUsd;
      totalProfitUsd += tradeProfitUsd;
      totalProfit += tradeProfit;
      totalProfitRelative +=
        (trade.valueInUsd / (Number(accountInfo.usdValue) / 100)) * tradeProfit;

      if (trade.profit >= 0) {
        positiveTrades++;
      } else {
        negativeTrades++;
      }

      shortTrades++;
    } else {
      longTrades++;
    }

    const symbol = `<a href="${serverUrl}/coin/${trade.asset}">${trade.asset}</a>`;
    trades.push(`<tr>
            <td>
                <b>${symbol}</b>
                </td>
            <td>
            ${trade.position}
            </td>
            <td>
            ${new Date(trade.tradeDate).toLocaleTimeString()}
            </td>
            <td>
            ${Number(trade.price).toFixed(8)}
            </td>
            <td>
            ${trade.amount}
            </td>
            <td>
            $${trade.valueInUsd}
            </td>
            <td class="${trade.position === 'short' && tradeProfit > 0 ? 'text-success' : 'text-danger'}">
            ${trade.position === 'short' ? Number(tradeProfit).toFixed(2) + '%' : ''}
            </td>
            <td>
            ${trade.position === 'short' ? Number(tradeProfitUsd).toFixed(2) : ''}
            </td>
        </tr>`);
  }

  const profitClass = totalProfit > 0 ? 'text-success' : 'text-danger';
  const profitUsdClass = totalProfitUsd > 0 ? 'text-success' : 'text-danger';

  const btcRender =
    '<small><span class="' +
    (btcPercent > 0 ? 'text-success' : 'text-danger') +
    '">' +
    (btcPercent >= 0 ? '+' : '') +
    btcPercent +
    '%</span></small>';

  const usdRender =
    '<small><span class="' +
    (usdPercent > 0 ? 'text-success' : 'text-danger') +
    '">' +
    (usdPercent >= 0 ? '+' : '') +
    usdPercent +
    '%</span></small>';

  const percentWins = Number(
    (positiveTrades / (positiveTrades + negativeTrades)) * 100,
  );
  const percentWinsRender =
    '<span class="' +
    (percentWins > 0 ? 'text-success' : 'text-danger') +
    '">' +
    percentWins.toFixed(2) +
    '%</span>';

  content.infoRows = [
    {
      name: 'Current BTC Balance',
      value:
        Number(accountInfo.btcValue).toFixed(3) + ' BTC (' + btcRender + ')',
    },
    {
      name: 'Current USD Balance',
      value:
        '$' + Number(accountInfo.usdValue).toFixed(2) + ' (' + usdRender + ')',
    },
    {
      name: 'Bot Profit Today RTB',
      value:
        '<b><span class="' +
        profitClass +
        '">' +
        Number(totalProfitRelative).toFixed(2) +
        '%</span></b>',
    },
    {
      name: 'Bot Profit Today %',
      value:
        '<b><span class="' +
        profitClass +
        '">' +
        Number(totalProfit).toFixed(2) +
        '%</span></b>',
    },
    {
      name: 'Bot Profit Today USD',
      value:
        '<b><span class="' +
        profitUsdClass +
        '">$' +
        Number(totalProfitUsd).toFixed(2) +
        '</span></b>',
    },
    {
      name: 'Number trades',
      value:
        content.trades.length +
        ' (' +
        shortTrades +
        ' short | ' +
        longTrades +
        ' long)',
    },
    {
      name: 'Statistic',
      value:
        percentWinsRender +
        '% wins | ' +
        positiveTrades +
        ' wins | ' +
        negativeTrades +
        ' loses',
    },
  ];

  const infoRows = content.infoRows.map((infoRow) => {
    return `<tr>
        <td width="150px">
            <span ><b>${infoRow.name}</b></span>
        </td>
        <td>
            <span>
            ${infoRow.value}
        </td>
        </tr>`;
  });

  return `<!DOCTYPE html>
            <html lang="en">
            <head>
                     <style>
                          html {
            font-family: Arial, serif;
            font-size: 13px;
        }

        body {
        }
        
        a {
    color: rgba(51,48,49,0.84);
    text-decoration: none;
}

        .top {
            height: 30px;
            text-align: center;
            color: #cacaca;
            font-weight: bold;
            font-size: 14px;
            padding-top: 10px;
        }
        
        .text-success {
    color: #35a66e;
}

.text-danger {
    color: #c64a44;
}

        .panel {
            border: 1px solid #525252;
            background-color: hsla(0, 0%, 53.3%, .07);
            -webkit-box-shadow: none;
            -moz-box-shadow: none;
            box-shadow: none;
            color: #949ba2;
            border-radius: 3px;
            margin-bottom: 20px;

            top: 50px;
        }

        .panel-head {
            color: #cacaca;
            padding: 5px 10px;
            border-bottom: 1px solid hsla(0, 0%, 45.9%, .29);
            border-top-left-radius: 3px;
            border-top-right-radius: 3px;
            background-color: #363841;
            font-weight: 700;
            text-transform: uppercase;
        }

        .panel-body {
            padding: 10px 15px 15px;
        }

        .panel-filled {
            border: 1px solid #525252;
            background-color: hsla(0, 0%, 53.3%, .07);
        }

        .row {
            margin-right: -15px;
            margin-left: -15px;
        }

        table tr td:nth-child(2) {
            font-weight: bold;
        }

        .accent {
            color: #f6a821;
        }

        .row-text {
            margin: 15px;
            font-weight: 400;
        }

        .table.table-no-border {
            margin-bottom: 0;
        }

        .table {
            width: 100%;
            max-width: 100%;
            margin-bottom: 20px;
            background-color: transparent;
            border-spacing: 0;
            border-collapse: collapse;
            text-align: left;
        }

        .table>caption+thead>tr:first-child>td, .table>caption+thead>tr:first-child>th, .table>colgroup+thead>tr:first-child>td, .table>colgroup+thead>tr:first-child>th, .table>thead:first-child>tr:first-child>td, .table>thead:first-child>tr:first-child>th {
            border-top: 0;
        }

        .small, small {
            font-size: 85%;
        }

        .table > tbody > tr > td, .table > tbody > tr > th, .table > tfoot > tr > td, .table > tfoot > tr > th, .table > thead > tr > td, .table > thead > tr > th {
            border-top: 1px solid rgba(61, 64, 76, 0.42);
        }

        .table-condensed > tbody > tr > td, .table-condensed > tbody > tr > th, .table-condensed > tfoot > tr > td, .table-condensed > tfoot > tr > th, .table-condensed > thead > tr > td, .table-condensed > thead > tr > th {
            padding: 5px;
        }

        .panel-filled .table > tbody > tr > td, .panel-filled .table > tbody > tr > th, .panel-filled .table > tfoot > tr > td, .panel-filled .table > tfoot > tr > th, .panel-filled .table > thead > tr > td, .panel-filled .table > thead > tr > th {
            border-color: rgba(72, 76, 90, 0.12);
        }

        .table > tbody > tr > td, .table > tbody > tr > th, .table > tfoot > tr > td, .table > tfoot > tr > th, .table > thead > tr > td, .table > thead > tr > th {
            vertical-align: top;
            border-top: 1px solid #ddd;
        }
                     </style>
                     </head>
            <body>
            <div>
                <div class="panel panel-filled panel-c-primary">
                    <div class="panel-head">Moonbot | Daily Autobot Report | ${date}</div>
                    <div class="panel-body">
                        <d>
                            <table width="100%" class="table table-condensed table-no-border">
                                <tbody>
                                ${infoRows.join('')}
                                </tbody>
                            </table>
                        </d>
                    </div>
                </div>
                
                <div class="panel panel-filled panel-c-primary">
                    <div class="panel-head">Bot Trades</div>
                    <div class="panel-body">

            <table width="100%" class="table table-sm table">
                            <thead>
                            <tr>
                                <th width="">Symbol</th>
                                <th>Advice</th>
                                <th>Trade Date</th>
                                <th>Price</th>
                                <th>Units</th>
                                <th>Value in $</th>
                                <th>Profit %</th>
                                <th>Profit USD</th>
                            </thead>
                            <tbody>
                                ${trades.join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            </body>
            </html>`;
};

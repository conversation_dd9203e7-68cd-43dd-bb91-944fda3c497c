import { Injectable } from '@nestjs/common';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { DbLogService } from '@app/database/db-log/db-log.service';
import {
  BinanceAccountInfoService,
  MbAccountInfo,
} from '@app/binance-api/binance-account-info/binance-account-info.service';
import { LongFunctionsService } from '@app/auto-bot-processor/long-functions/long-functions.service';
import { ShortFunctionsService } from '@app/auto-bot-processor/short-functions/short-functions.service';
import { SendMailService } from '@app/send-mail';
import { DbMarketBotDocument } from '@app/database/db-market-bot/db-market-bot.model';
import { DbAutoBotDocument } from '@app/database/db-auto-bot/db-auto-bot.model';
import { serverUrl } from '../../../../utils/currentServer';
import { DbAutoBotService } from '@app/database/db-auto-bot/db-auto-bot.service';
import { DbMarketBotService } from '@app/database/db-market-bot/db-market-bot.service';
import { ArrayUtils } from '../../../../utils/ArrayUtils';
import { DbWhiteListService } from '@app/database/db-white-list/db-white-list.service';
import { DbMacdService } from '@app/database/db-macd/db-macd.service';
import { DbIndicator } from '@app/database/db-indicator/db-indicator.model';
import { Exchange } from '../../../../types/exchanges';
import { GateioAccountInfoService } from '@app/gateio-api/gateio-account-info/gateio-account-info.service';

@Injectable()
export class BuySellService {
  constructor(
    private coinPriceService: DbCoinPriceService,
    private logger: DbLogService,
    private binanceAccountInfoService: BinanceAccountInfoService,
    private gateioAccountInfoService: GateioAccountInfoService,
    private longFunctionService: LongFunctionsService,
    private shortFunctionsService: ShortFunctionsService,
    private sendMailService: SendMailService,
    private autobotService: DbAutoBotService,
    private marketBotService: DbMarketBotService,
    private whitelistService: DbWhiteListService,
    private macdService: DbMacdService,
  ) {}

  async processSell(
    marketBot: DbMarketBotDocument,
    autobot: DbAutoBotDocument,
    accountInfo: MbAccountInfo,
  ) {
    let acInfo = accountInfo;
    const { symbol } = marketBot;
    const { strategy, interval } = autobot;

    const currentCoinPrice = await this.coinPriceService.dbGetCoinPrice(
      symbol,
      autobot.exchange,
    );
    const trimmedCurrentCoinPrice = Number(Number(currentCoinPrice).toFixed(8));

    this.logger.logOrders(
      symbol,
      autobot.exchange,
      `-> Sell on ${strategy} ${interval} indicator`,
    );
    this.logger.logOrders(
      symbol,
      autobot.exchange,
      '-> Current coin price',
      trimmedCurrentCoinPrice.toFixed(8),
    );
    this.logger.logOrders(
      symbol,
      autobot.exchange,
      '-> Fetching account infos...',
    );

    if (!acInfo) {
      switch (autobot.exchange) {
        case Exchange.BINANCE:
          acInfo = await this.binanceAccountInfoService.getAccountInfoWithRetry(
            autobot.userId,
          );
          break;
        case Exchange.GATEIO:
          acInfo = await this.gateioAccountInfoService.getAccountInfoWithRetry(
            autobot.userId,
          );
          break;
      }
    }

    try {
      await this.shortFunctionsService.shortAutoBot(
        autobot,
        {
          price: trimmedCurrentCoinPrice,
          action: 'short',
          symbol,
          indicator: strategy,
          interval: interval,
          profit: null,
          prediction: null,
          newAdded: true,
          timestamp: new Date(),
          candleTime: new Date(),
          exchange: autobot.exchange,
        } as DbIndicator,
        acInfo,
      );

      await this.sendSellMail(autobot, symbol, currentCoinPrice);
    } catch (e) {
      this.logger.errorOrders(e);
    }

    this.logger.logOrders(symbol, autobot.exchange, '-> Sell finished');
  }

  public async processBuy(
    symbol: string,
    autobot: DbAutoBotDocument,
    accountInfo: MbAccountInfo,
  ) {
    const { strategy, interval } = autobot;
    let acInfo = accountInfo;

    const currentCoinPrice = await this.coinPriceService.dbGetCoinPrice(
      symbol,
      autobot.exchange,
    );
    const trimmedCurrentCoinPrice = Number(Number(currentCoinPrice).toFixed(8));

    const currentAutobot = await this.autobotService.dbGetAutoBot(autobot._id);
    const marketBots = await this.marketBotService.dbGetMarketBotsForAutobot(
      currentAutobot._id.toString(),
    );
    if (marketBots.length >= currentAutobot.numbots) {
      this.logger.warnBot(
        symbol,
        `-> Max bot count reached ${marketBots.length}/${currentAutobot.numbots}.`,
      );
    }

    this.logger.logOrders(
      symbol,
      autobot.exchange,
      `-> Buy on ${strategy} ${interval} indicator`,
    );
    this.logger.logOrders(
      symbol,
      autobot.exchange,
      '-> Current coin price',
      trimmedCurrentCoinPrice.toFixed(8),
    );
    this.logger.logOrders(
      symbol,
      autobot.exchange,
      '-> Fetching account infos...',
    );

    if (!acInfo) {
      switch (autobot.exchange) {
        case Exchange.BINANCE:
          acInfo = await this.binanceAccountInfoService.getAccountInfoWithRetry(
            currentAutobot.userId,
          );
          break;
        case Exchange.GATEIO:
          acInfo = await this.gateioAccountInfoService.getAccountInfoWithRetry(
            currentAutobot.userId,
          );
          break;
      }
    }

    const usdtBalance = acInfo.balances.find((x) => x.asset === 'USDT') || null;

    if (usdtBalance.usdValue <= 5) {
      this.logger.errorOrders(
        symbol,
        autobot.exchange,
        '-> Not enough credit for long trade',
      );

      return;
    }

    const checkBtcCross = await this.checkBtcCrossIndicator(autobot, symbol);
    if (!checkBtcCross) {
      return;
    }

    try {
      await this.longFunctionService.longAutoBot(
        currentAutobot,
        {
          price: trimmedCurrentCoinPrice,
          action: 'long',
          symbol,
          indicator: strategy,
          timestamp: new Date(),
          newAdded: true,
          interval: interval,
          profit: null,
          prediction: null,
          candleTime: new Date(),
          exchange: autobot.exchange,
        } as DbIndicator,
        acInfo,
      );

      await this.sendBuyMail(currentAutobot, symbol, currentCoinPrice);
    } catch (e) {
      this.logger.errorOrders(symbol, autobot.exchange, e);
    }

    this.logger.logOrders(symbol, autobot.exchange, '-> Buy finished');
  }

  public async fillPortfolio(
    autobot: DbAutoBotDocument,
    newAdvice: DbIndicator,
    accountInfo: MbAccountInfo,
  ) {
    try {
      const marketBots =
        await this.marketBotService.dbGetMarketBotsFromAutobot(autobot);
      const marketBotSymbols = marketBots.map((x) => x.symbol);

      const whitelist = await this.whitelistService.dbGetWhiteListCoins(
        autobot.userId,
        autobot.exchange,
      );
      const canBuySymbols = whitelist
        .map((x) => x.symbol)
        .filter((x) => !marketBotSymbols.includes(x));

      const randomSymbol = ArrayUtils.random(canBuySymbols);

      this.logger.logOrders(
        newAdvice.symbol,
        autobot.exchange,
        `-> Select random coin: ${randomSymbol}`,
      );

      // Покупаем рандомный коин
      await this.processBuy(randomSymbol, autobot, accountInfo);
    } catch (e) {
      this.logger.errorOrders(newAdvice.symbol, '->', e.message);
    }
  }

  private async sendSellMail(
    autobot: DbAutoBotDocument,
    symbol: string,
    currentCoinPrice: number,
  ) {
    await this.sendMailService.sendOrderMail(
      autobot.userId,
      'Moonbot ' + symbol + ' ' + autobot.strategy,
      autobot.strategy + ' ' + autobot.interval,
      {
        'Symbol:': `<a href="${serverUrl}/coin/${symbol}">${symbol}</a>`,
        'Sell Price:': `${currentCoinPrice}`,
        'Timestamp:': `${new Date().toLocaleString('de-DE')}`,
      },
      true,
    );
  }

  private async sendBuyMail(
    autobot: DbAutoBotDocument,
    symbol: string,
    currentCoinPrice: number,
  ) {
    await this.sendMailService.sendOrderMail(
      autobot.userId,
      'Moonbot ' + symbol + ' ' + autobot.strategy,
      autobot.strategy + ' ' + autobot.interval,
      {
        'Symbol:': `<a href="${serverUrl}/coin/${symbol}">${symbol}</a>`,
        'Buy Price:': `${currentCoinPrice}`,
        'Timestamp:': `${new Date().toLocaleString('de-DE')}`,
      },
      true,
    );
  }

  private async checkBtcCrossIndicator(
    autobot: DbAutoBotDocument,
    symbol: string,
  ) {
    if (autobot.useBtcAOCross === true) {
      const aoIndicator = await this.macdService.getMACD(
        {
          symbol: 'BTCUSDT',
          interval: '1d',
        },
        autobot.exchange,
      );

      const signal = aoIndicator.signal;

      if (signal != null && signal < 0) {
        this.logger.warnOrders(
          symbol,
          '-> BTC AO Indicator < 0. ' + "Don't process",
        );
        return false;
      } else {
        this.logger.logOrders(
          symbol,
          autobot.exchange,
          '-> BTC AO Indicator Check OK: ' + Number(signal).toFixed(8) + ' > 0',
        );
        return true;
      }
    }

    return true;
  }
}

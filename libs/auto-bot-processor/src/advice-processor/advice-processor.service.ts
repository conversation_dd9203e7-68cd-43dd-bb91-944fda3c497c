import { Injectable } from '@nestjs/common';
import PriceCalculation from '../../../../utils/PriceCalculation';
import { sendChannelMessage } from '../../../../utils/notifications';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { TradeProcessorService } from '@app/auto-bot-processor/trade-processor/trade-processor.service';
import { MarketBotAdviceProcessorService } from '@app/auto-bot-processor/market-bot-advice-processor/market-bot-advice-processor.service';
import { DbIndicatorService } from '@app/database/db-indicator/db-indicator.service';
import { Exchange } from '../../../../types/exchanges';
import { DbAutoBotService } from '@app/database/db-auto-bot/db-auto-bot.service';
import { Advice } from '../../../../types/advice';
import { DbAutoBotDocument } from '@app/database/db-auto-bot/db-auto-bot.model';
import { DbIndicatorDocument } from '@app/database/db-indicator/db-indicator.model';

@Injectable()
export class AdviceProcessorService {
  constructor(
    private logger: DbLogService,
    private neuralTradeProcessorService: TradeProcessorService,
    private marketBotAdviceService: MarketBotAdviceProcessorService,
    private indicatorService: DbIndicatorService,
    private autobotService: DbAutoBotService,
  ) {}

  processNewAdvices = async () => {
    const newAdvices = await this.indicatorService.getNewIndicators();

    for (const newAdvice of newAdvices) {
      await this.indicatorService.setNewIndicatorsToFalse(newAdvice._id);
    }

    const autobots = await this.autobotService.dbGetAllAutobots();
    const activeAutobots = autobots.filter((x) => x.active == true);

    for (const autobot of activeAutobots) {
      for (const newAdvice of newAdvices.filter(
        (x) => x.action == Advice.SHORT,
      )) {
        await this.processAdvice(autobot, newAdvice);
      }

      for (const newAdvice of newAdvices.filter(
        (x) => x.action == Advice.LONG,
      )) {
        await this.processAdvice(autobot, newAdvice);
      }
    }
  };

  private async processAdvice(
    autobot: DbAutoBotDocument,
    newAdvice: DbIndicatorDocument,
  ) {
    if (autobot.exchange != newAdvice.exchange) {
      return;
    }

    try {
      if (
        autobot.interval == newAdvice.interval &&
        autobot.strategy == newAdvice.indicator
      ) {
        // this.logger.logOrders(
        //   'Processing new indicator for',
        //   newAdvice.symbol,
        //   newAdvice.indicator,
        //   newAdvice.interval,
        //   newAdvice.exchange,
        // );

        await this.neuralTradeProcessorService.processAdvice(
          newAdvice,
          autobot,
        );
      }
      // await this.marketBotAdviceService.processManualOrders(newAdvice);

      const sendTelegram = false;
      if (sendTelegram) {
        await this.sendTelegramMessage(newAdvice);
      }
    } catch (e) {
      this.logger.errorOrders(newAdvice.symbol, '->', e.message);
    }
  }

  sendTelegramMessage = async (newAdvice) => {
    const { strategy, advice, symbol, price } = newAdvice;

    if (strategy === 'nesterov') {
      try {
        const lastLong = await this.indicatorService.getLastIndicatorAction(
          symbol,
          '15',
          strategy,
          Exchange.BINANCE,
          'long',
        );
        const lastPrice = Number(lastLong.price);
        const percentDiff = PriceCalculation.toPercentGain(lastPrice, price);

        await sendChannelMessage(
          `${symbol} ${advice} @${price} (${percentDiff}%) https://moonbot.dsserv.de/coin/${symbol}`,
        );
      } catch (e) {
        this.logger.errorOrders('sendTelegramMessage for ', symbol, e);
      }
    }
  };
}

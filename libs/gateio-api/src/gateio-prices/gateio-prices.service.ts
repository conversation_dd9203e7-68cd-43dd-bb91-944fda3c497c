import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { GateioConnectService } from '@app/gateio-api/gateio-connect/gateio-connect.service';
import { Exchange } from '../../../../types/exchanges';

@Injectable()
export class GateioPricesService {
  constructor(
    private logger: DbLogService,
    private coinPriceService: DbCoinPriceService,
    private gateioConnectService: GateioConnectService,
  ) {}

  async getPrices() {
    try {
      const client = this.gateioConnectService.init();
      const prices = await client.getSpotTicker();

      for (const item of prices) {
        await this.coinPriceService.dbSetCurrentCoinPrice(
          item.currency_pair.replace('_', ''),
          Number(item.last),
          Exchange.GATEIO,
        );
      }
    } catch (e) {
      this.logger.errorServer(e.message, e);
    }

    return null;
  }
}

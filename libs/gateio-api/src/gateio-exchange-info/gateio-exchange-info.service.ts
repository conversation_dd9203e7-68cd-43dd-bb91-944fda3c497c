import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { GateioConnectService } from '../gateio-connect/gateio-connect.service';
import { ExchangeInfo } from 'binance-api-node';

@Injectable()
export class GateioExchangeInfoService {
  constructor(
    private logger: DbLogService,
    private connect: GateioConnectService,
  ) {}

  async getExchangeInfo(): Promise<ExchangeInfo> {
    const client = this.connect.init();
    try {
      const result = await client.getSpotCurrencyPairs();

      return {
        timezone: null,
        exchangeFilters: null,
        rateLimits: null,
        serverTime: null,
        symbols: result
          .filter((x) => x.quote == 'USDT')
          .filter((x) => x.trade_status == 'tradable')
          .map((x) => {
            return {
              symbol: x.base + x.quote,
              baseAsset: x.base,
              quoteAsset: x.quote,
              status: 'TRADING',
              baseAssetPrecision: x.precision,
              quotePrecision: x.precision,
              isSpotTradingAllowed: true,
              pricePrecision: x.precision,
              quantityPrecision: x.amount_precision,
              quoteAssetPrecision: x.precision,
              isMarginTradingAllowed: false,
              filters: [
                {
                  filterType: 'LOT_SIZE',
                  minQty: x.min_base_amount,
                  maxQty: x.max_base_amount,
                  stepSize: (1 / Math.pow(10, x.amount_precision)).toFixed(8),
                },
                {
                  filterType: 'PRICE_FILTER',
                  minPrice: '0.01',
                  maxPrice: '9999999',
                  tickSize: (1 / Math.pow(10, x.precision)).toFixed(8),
                },
              ],
              ocoAllowed: false,
              orderTypes: null,
              icebergAllowed: false,
              permissions: null,
              baseCommissionPrecision: 0,
              quoteOrderQtyMarketAllowed: false,
              quoteCommissionPrecision: 0,
            };
          }),
      } as ExchangeInfo;
    } catch (e) {
      this.logger.errorServer(e.message, e);
    }

    return null;
  }
}

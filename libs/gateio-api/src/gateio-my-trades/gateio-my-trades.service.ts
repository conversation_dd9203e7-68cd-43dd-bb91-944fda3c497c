import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { GateioConnectService } from '@app/gateio-api/gateio-connect/gateio-connect.service';
import { Db<PERSON><PERSON> } from '@app/database/db-key/db-key.model';
import { MyTrade } from 'binance-api-node';

@Injectable()
export class GateioMyTradesService {
  constructor(
    private logger: DbLogService,
    private connect: GateioConnectService,
  ) {}

  async getMyTrades(
    apiKey: DbKey,
    args: GQL.IMytradesOnExchangeQueryArguments,
  ): Promise<MyTrade[]> {
    const client = this.connect.init(apiKey.key, apiKey.secret);

    try {
      const history = await client.getSpotTradingHistory({
        currency_pair: args.symbol.slice(0, -4) + '_USDT',
        limit: 10,
        account: 'spot',
      });

      const result = history.map((x) => {
        return {
          id: Number(x.id),
          symbol: x.currency_pair,
          orderId: Number(x.order_id),
          orderListId: Number(x.order_id),
          price: x.price,
          qty: x.amount,
          quoteQty: x.amount,
          commission: x.fee,
          commissionAsset: x.fee_currency,
          time: new Date(Number(x.create_time_ms)).getTime(),
          isBuyer: x.side == 'buy',
          isMaker: x.role == 'maker',
          isBestMatch: true,
        } as MyTrade;
      });

      return result.reverse();
    } catch (e) {
      this.logger.errorServer(e.message, e);
    }
  }
}

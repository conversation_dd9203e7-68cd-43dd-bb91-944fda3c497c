import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { GateioConnectService } from '@app/gateio-api/gateio-connect/gateio-connect.service';
import { QueryOrderResult } from 'binance-api-node';
import { GetSpotOrdersReq, SpotOrder } from 'gateio-api';

@Injectable()
export class GateioOpenOrdersService {
  constructor(
    private logger: DbLogService,
    private connectService: GateioConnectService,
  ) {}

  async getOpenOrders(apiKey, args: any) {
    const client = this.connectService.init(apiKey.key, apiKey.secret);
    try {
      const query: GetSpotOrdersReq | any = {};

      if (args && args.symbol) {
        query.currency_pair = args.symbol.slice(0, -4) + '_USDT';
        query.status = 'open';
        const orders = await client.getSpotOrders(query);

        return orders.map((x) => {
          return {
            symbol: x.currency_pair.replace('_', ''),
            orderId: Number(x.id),
            clientOrderId: x.id,
            orderListId: Number(x.id),
            side: this.mapSide(x.side),
            type: x.type == 'limit' ? 'LIMIT' : 'MARKET',
            executedQty: x.filled_amount,
            origQty: x.amount,
            icebergQty: x.iceberg,
            price: x.price,
            time: new Date(x.create_time_ms).getTime(),
            isWorking: null,
            status: this.mapStatus(x.status),
            origQuoteOrderQty: x.amount,
            stopPrice: null,
            timeInForce: null,
            updateTime: new Date(x.update_time_ms).getTime(),
            cummulativeQuoteQty: x.amount,
          } as QueryOrderResult;
        });
      }

      return client.getSpotOpenOrders(query);
    } catch (e) {
      this.logger.errorServer(e.message, e);
      return [];
    }
  }

  mapStatus(status: string): string {
    switch (status) {
      case 'open':
        return 'NEW';
      case 'canceled':
        return 'CANCELED';
      case 'closed':
        return 'FILLED';
    }
  }

  mapSide(side: string): string {
    switch (side) {
      case 'buy':
        return 'BUY';
      case 'sell':
        return 'SELL';
    }
  }
}

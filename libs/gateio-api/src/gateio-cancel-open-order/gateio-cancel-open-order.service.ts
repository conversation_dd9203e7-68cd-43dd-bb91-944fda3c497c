import { Injectable } from '@nestjs/common';
import { GateioConnectService } from '@app/gateio-api/gateio-connect/gateio-connect.service';
import { Db<PERSON><PERSON> } from '@app/database/db-key/db-key.model';
import { CancelOrderResult } from 'binance-api-node';
import { DbLogService } from '@app/database/db-log/db-log.service';

@Injectable()
export class GateioCancelOpenOrderService {
  constructor(
    private connectService: GateioConnectService,
    private logger: DbLogService,
  ) {}

  async cancelOpenOrder(apiKey: DbKey, args: any): Promise<CancelOrderResult> {
    try {
      const client = this.connectService.init(apiKey.key, apiKey.secret);

      const result = await client.cancelSpotOrder({
        currency_pair: args.symbol.slice(0, -4) + '_USDT',
        order_id: args.orderId,
      });

      return {
        symbol: result.currency_pair.replace('_', ''),
        orderId: Number(result.id),
        side: result.side == 'buy' ? 'BUY' : 'SELL',
        price: result.price,
        clientOrderId: result.id,
        orderListId: Number(result.id),
        executedQty: result.filled_amount,
        origQty: result.amount,
        type: result.type == 'market' ? 'MARKET' : 'LIMIT',
        status: result.status,
        timeInForce: result.time_in_force,
        origClientOrderId: result.id,
        cummulativeQuoteQty: result.amount,
      } as CancelOrderResult;
    } catch (e) {
      this.logger.errorServer(e);
    }
  }
}

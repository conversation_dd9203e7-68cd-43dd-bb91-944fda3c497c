import { Injectable } from '@nestjs/common';
import { DbSaldoService } from '@app/database/db-saldo/db-saldo.service';
import { DbSaldoUsdService } from '@app/database/db-saldo-usd/db-saldo-usd.service';
import { DbBotOrderService } from '@app/database/db-bot-order/db-bot-order.service';
import { DbTradeService } from '@app/database/db-trade/db-trade.service';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbKey } from '@app/database/db-key/db-key.model';
import { Order, OrderFill, OrderStatus_LT } from 'binance-api-node';
import { MBOrderType } from '../../../../types/binance.types';
import { Exchange } from '../../../../types/exchanges';
import { GateioConnectService } from '@app/gateio-api/gateio-connect/gateio-connect.service';
import { GateioAccountInfoService } from '@app/gateio-api/gateio-account-info/gateio-account-info.service';
import { SpotOrder } from 'gateio-api';
import PriceCalculation from '../../../../utils/PriceCalculation';

@Injectable()
export class GateioNewOrderService {
  constructor(
    private saldoService: DbSaldoService,
    private saldoUsdService: DbSaldoUsdService,
    private botOrderService: DbBotOrderService,
    private tradeService: DbTradeService,
    private connectService: GateioConnectService,
    private accountInfoService: GateioAccountInfoService,
    private logger: DbLogService,
  ) {}

  /**
   * [
   *   {
   *     _id: ObjectId('67b732df16ea39eb63dbf886'),
   *     user: '5afc918204b5f54b6dfdfc63',
   *     symbol: 'XRP5LUSDT',
   *     type: 'MARKET',
   *     quantity: 133.53,
   *     timestamp: ISODate('2025-02-20T13:49:19.960Z'),
   *     side: 'BUY',
   *     result: {
   *       symbol: 'XRP5LUSDT',
   *       price: '0.27812',
   *       origQty: '37.13736',
   *       executedQty: '132.71',
   *       type: 'MARKET',
   *       status: 'FILLED',
   *       side: 'BUY',
   *       orderId: ************,
   *       orderListId: ************,
   *       transactTime: *************,
   *       time: *************,
   *       updateTime: *************,
   *       clientOrderId: '************',
   *       cummulativeQuoteQty: '37.13736',
   *       stopPrice: null,
   *       timeInForce: null,
   *       fills: [
   *         {
   *           price: '0.27812',
   *           qty: '37.13736',
   *           commission: '0',
   *           tradeId: 0,
   *           commissionAsset: 'USDT'
   *         }
   *       ],
   *       isIsolated: null,
   *       icebergQty: null,
   *       isWorking: null
   *     },
   *     exchange: 'gateio',
   *     __v: 0
   *   }
   * ]
   *
   *
   * @param userId
   * @param apiKey
   * @param exchangeInfo
   * @param currentCoinPrice
   * @param args
   */
  public async newOrder(
    userId: string,
    apiKey: DbKey,
    exchangeInfo: any,
    currentCoinPrice: number,
    args: GQL.IOrderMutationOnMutationArguments,
  ) {
    const client = this.connectService.init(apiKey.key, apiKey.secret);

    try {
      let result: Order | undefined;

      const defaultProps = {
        recvWindow: args.recvWindow ? args.recvWindow : 10000,
      };

      const price = PriceCalculation.getFixedPrice(
        args.price,
        exchangeInfo,
        args.symbol,
      );

      const priceMarket = currentCoinPrice;

      const amount = PriceCalculation.getFixedAmount(
        args.quantity,
        exchangeInfo.find((x) => x.symbol === args.symbol),
      );

      const amountMarket = args.side == 'BUY' ? args.quantityUsd : amount;

      switch (args.type) {
        case 'LIMIT':
          const localResult = await client.submitSpotOrder({
            currency_pair: args.symbol.slice(0, -4) + '_USDT',
            side: args.side == 'BUY' ? 'buy' : 'sell',
            type: 'limit',
            amount: String(amount),
            account: 'spot',
            price: String(price),
            iceberg: '0',
          });
          result = this.mapResult(localResult, priceMarket);
          break;

        case 'MARKET':
          const localMarketResult = await client.submitSpotOrder({
            currency_pair: args.symbol.slice(0, -4) + '_USDT',
            side: args.side == 'BUY' ? 'buy' : 'sell',
            type: 'market',
            amount: String(amountMarket),
            account: 'spot',
            time_in_force: null,
            iceberg: '0',
          });
          console.log(localMarketResult);
          result = this.mapResult(localMarketResult, priceMarket);

          break;

        case 'STOP_LOSS_LIMIT':
          const stopLossOrder = await client.submitSpotPriceTriggerOrder({
            trigger: {
              price: String(args.stopPrice),
              rule: '<=',
              expiration: 9999999,
            },
            put: {
              type: 'limit',
              side: 'sell',
              price: String(price),
              amount: String(amount),
              account: 'normal',
            },
            market: args.symbol.slice(0, -4) + '_USDT',
          });
          result = null;
          break;

        default:
          return;
      }

      await this.saveUserSaldo(args, apiKey, result, userId);

      if (
        args.side === 'BUY' &&
        (result.status === 'FILLED' || result.status === 'PARTIALLY_FILLED')
      ) {
        await this.addAutoStopLossOrder(args, userId);
        await this.addAutoTakeProfitOrder(args, userId);
      }

      if (
        args.side === 'SELL' &&
        (result.status === 'FILLED' || result.status === 'PARTIALLY_FILLED')
      ) {
        await this.removeAutoSLTPOrders(args, userId);
      }

      return result;
    } catch (e) {
      this.logger.errorServer(e.message, e);
      throw {
        message: e.body.message,
      };
    }
  }

  private async addAutoTakeProfitOrder(
    args: GQL.IOrderMutationOnMutationArguments,
    userId: string,
  ) {
    if (args.buyTPActive) {
      await this.botOrderService.dbAddBotOrder(userId, Exchange.GATEIO, {
        ...args,
        type: MBOrderType.AUTO_TAKE_PROFIT,
        price: args.botTPPrice,
      });
    }
  }

  private async removeAutoSLTPOrders(
    args: GQL.IOrderMutationOnMutationArguments,
    userId: string,
  ) {
    const typesToRemove = [
      MBOrderType.AUTO_TAKE_PROFIT,
      MBOrderType.AUTO_STOP_LOSS,
      MBOrderType.BOT_TAKE_PROFIT,
      MBOrderType.BOT_STOP_LOSS,
    ];

    try {
      const result = await this.botOrderService.dbGetBotOrders(
        userId,
        false,
        Exchange.GATEIO,
      );

      for (const order of result) {
        if (order.symbol === args.symbol) {
          await this.botOrderService.dbDeleteBotOrder(
            userId,
            order._id.toString(),
          );
        }
      }
    } catch (e) {
      this.logger.errorServer(e);
    }
  }

  private async addAutoStopLossOrder(
    args: GQL.IOrderMutationOnMutationArguments,
    userId: string,
  ) {
    if (args.buyStopActive) {
      await this.botOrderService.dbAddBotOrder(userId, Exchange.GATEIO, {
        ...args,
        type: MBOrderType.AUTO_STOP_LOSS,
        price: args.botStopPrice,
      });
    }
  }

  private async saveUserSaldo(
    args: GQL.IOrderMutationOnMutationArguments,
    apiKey: DbKey,
    result: Order,
    userId: string,
  ) {
    const argsModified = { ...args, noSmallAmount: false };
    const accountInfo = await this.accountInfoService.getAccountInfo(
      apiKey,
      argsModified,
    );

    if (result.status === 'FILLED') {
      await this.saldoService.saveUserSaldo(
        userId,
        accountInfo,
        Exchange.GATEIO,
      );
      await this.saldoUsdService.saveUserSaldoUsd(
        userId,
        accountInfo,
        Exchange.GATEIO,
      );
    }

    await this.tradeService.saveUserTrade(
      userId,
      argsModified,
      result,
      Exchange.GATEIO,
    );
  }

  async newBotOrder(userId: string, args: any) {
    const result = await this.botOrderService.dbAddBotOrder(
      userId,
      Exchange.GATEIO,
      args,
    );

    if (args.buyStopActive) {
      await this.botOrderService.dbAddBotOrder(userId, Exchange.GATEIO, {
        ...args,
        type: 'AUTO_STOP_LOSS',
        price: args.botStopPrice,
      });
    }

    if (args.buyTPActive) {
      await this.botOrderService.dbAddBotOrder(userId, Exchange.GATEIO, {
        ...args,
        type: 'AUTO_TAKE_PROFIT',
        price: args.botTPPrice,
      });
    }

    return {
      symbol: result.symbol,
      orderId: result._id.toString(),
      transactTime: new Date(result.addTimestamp).getTime(),
      price: result.price,
      origQty: result.amount,
      executedQty: 0,
      status: 'NEW',
      type: result.type,
      ok: true,
    } as any;
  }

  mapStatus(status: string): OrderStatus_LT {
    switch (status) {
      case 'open':
        return 'NEW';
      case 'canceled':
        return 'CANCELED';
      case 'closed':
        return 'FILLED';
    }
  }

  mapSide(side: string): string {
    switch (side) {
      case 'buy':
        return 'BUY';
      case 'sell':
        return 'SELL';
    }
  }

  mapResult(localResult: SpotOrder, priceMarket: number): Order {
    console.log(localResult);

    return {
      symbol: localResult.currency_pair.replace('_', ''),
      price: String(priceMarket),
      origQty: localResult.amount,
      executedQty: localResult.filled_amount,
      type: localResult.type == 'market' ? 'MARKET' : 'LIMIT',
      status: this.mapStatus(localResult.status),
      side: localResult.side == 'buy' ? 'BUY' : 'SELL',
      orderId: Number(localResult.id),
      orderListId: Number(localResult.id),
      transactTime: new Date(localResult.update_time_ms).getTime(),
      time: new Date(localResult.create_time_ms).getTime(),
      updateTime: new Date(localResult.update_time_ms).getTime(),
      clientOrderId: localResult.id,
      cummulativeQuoteQty: localResult.amount,
      stopPrice: null,
      timeInForce: null,
      fee: Number(localResult.fee),
      finalQty: Number(localResult.filled_amount) - Number(localResult.fee),
      fills: [
        {
          price: String(priceMarket),
          qty: localResult.amount,
          commission: String(0),
          tradeId: 0,
          commissionAsset: 'USDT',
        } as OrderFill,
      ],
      isIsolated: null,
      icebergQty: null,
      isWorking: null,
    } as any;
  }
}

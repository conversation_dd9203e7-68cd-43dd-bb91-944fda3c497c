import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  DbStrategyTest,
  DbStrategyTestDocument,
} from '@app/database/db-strategy-test/db-strategy-test.model';

@Injectable()
export class DbStrategyTestService {
  constructor(
    @InjectModel(DbStrategyTest.name)
    private strategyTestDocumentModel: Model<DbStrategyTestDocument>,
  ) {}

  async dbGetStrategyTest(id: Types.ObjectId) {
    return this.strategyTestDocumentModel.findById(id);
  }

  dbCreateStrategyModel = async (
    args: any,
    userId: string,
    exchange: string,
  ) => {
    const {
      symbol,
      strategy,
      interval,
      startDate,
      isInternal,
      endDate,
      invest,
      tp,
      sl,
    } = args;

    const user = userId;

    try {
      return await new this.strategyTestDocumentModel({
        user,
        symbol,
        strategy,
        interval,
        invest,
        isInternal,
        exchange,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        status: 'New',
        tp,
        sl,
        createTimestamp: new Date(),
        posTrades: 0,
        negTrades: 0,
        longTrades: 0,
        shortTrades: 0,
        posNegRatio: 0,
        numTrades: 0,
      }).save();
    } catch (e) {
      throw e;
    }
  };

  dbPickNewStrategies = async () => {
    return this.strategyTestDocumentModel
      .find({
        status: 'New',
        isInternal: { $ne: true },
      })
      .lean()
      .exec();
  };

  dbGetStrategyTests = async (userId: string, exchange: string) => {
    return this.strategyTestDocumentModel
      .find({
        user: userId,
        isInternal: { $ne: true },
        exchange,
      })
      .sort({ createTimestamp: -1 })
      .limit(500)
      .lean()
      .exec();
  };

  updateStrategyTestStatus = async (id: string, status: string) => {
    await this.strategyTestDocumentModel
      .findOneAndUpdate(
        {
          _id: id,
        },
        {
          $set: {
            status,
          },
        },
      )
      .lean()
      .exec();
  };

  async updateTrade(args: {
    strategyId: Types.ObjectId;
    endUsd: number;
    count: number;
    posTrade: boolean;
    isBuy: boolean;
    endCount: number;
    botTrades?: any;
  }) {
    const { strategyId, endUsd, count, endCount, isBuy, posTrade, botTrades } =
      args;

    const entity = await this.strategyTestDocumentModel
      .findById(strategyId)
      .lean()
      .exec();

    const posTrades =
      isBuy === false && posTrade === true
        ? entity.posTrades + 1
        : entity.posTrades;
    const negTrades =
      isBuy === false && posTrade === false
        ? entity.negTrades + 1
        : entity.negTrades;
    const numTrades = isBuy === false ? entity.numTrades + 1 : entity.numTrades;
    const longTrades =
      isBuy === true ? entity.longTrades + 1 : entity.longTrades;
    const shortTrades =
      isBuy === false ? entity.shortTrades + 1 : entity.shortTrades;
    const posNegRatio =
      negTrades != 0 && posTrades != 0 ? posTrades / negTrades : 0;

    const update: any = {
      updateTimestamp: new Date(),
      count,
      endCount,
      posTrades,
      negTrades,
      longTrades,
      shortTrades,
      posNegRatio,
      numTrades,
      botTrades,
    };

    if (endUsd && !isNaN(endUsd)) {
      update.endUsd = endUsd;
    }

    await this.strategyTestDocumentModel
      .findOneAndUpdate(
        {
          _id: strategyId,
        },
        {
          $set: update,
        },
      )
      .lean()
      .exec();
  }

  async findByTestId(testId: string) {
    return this.strategyTestDocumentModel.findById(testId).lean().exec();
  }

  public async update(
    settings: any,
    botTrades: any,
    totalProfit: any,
    endUsd: number,
  ) {
    await this.strategyTestDocumentModel
      .findOneAndUpdate(
        {
          _id: settings.testId,
        },
        {
          $set: {
            botTrades,
            updateTimestamp: new Date(),
            profit: totalProfit,
            endUsd,
          },
        },
      )
      .lean()
      .exec();
  }
}

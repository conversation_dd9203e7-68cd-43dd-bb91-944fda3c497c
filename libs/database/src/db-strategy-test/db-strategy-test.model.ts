import { HydratedDocument } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbStrategyTestDocument = HydratedDocument<DbStrategyTest>;

@Schema({ collection: 'strategytesters' })
export class DbStrategyTest {
  @Prop()
  user: string;

  @Prop()
  symbol: string;

  @Prop()
  strategy: string;

  @Prop()
  isInternal: boolean;

  @Prop()
  profit: number;

  @Prop()
  invest: number;

  @Prop()
  endBtc: number;

  @Prop()
  endUsd: number;

  @Prop()
  interval: string;

  @Prop()
  tp: number;

  @Prop()
  sl: number;

  @Prop()
  count: number;

  @Prop()
  endCount: number;

  @Prop()
  numTrades: number;

  @Prop()
  posTrades: number;

  @Prop()
  negTrades: number;

  @Prop()
  longTrades: number;

  @Prop()
  shortTrades: number;

  @Prop()
  posNegRatio: number;

  @Prop({ type: Date })
  startDate: any;

  @Prop({ type: Date })
  endDate: any;

  @Prop()
  status: string;

  @Prop({ type: [Object] })
  roundTrips: any;

  @Prop({ type: [Object] })
  botTrades: [
    {
      tradeDate: any;
      position: string;
      price: number;
      profit: number;
      predictedChange: number;
    },
  ];

  @Prop({ type: Date })
  createTimestamp: any;

  @Prop({ type: Date })
  updateTimestamp: any;

  @Prop()
  exchange: string;
}

export const DbStrategyTestSchema =
  SchemaFactory.createForClass(DbStrategyTest);
DbStrategyTestSchema.index({ user: 1 });

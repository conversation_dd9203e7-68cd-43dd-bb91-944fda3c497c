import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import {
  DbWhiteList,
  DbWhiteListSchema,
} from '@app/database/db-white-list/db-white-list.model';
import { DbWhiteListService } from '@app/database/db-white-list/db-white-list.service';

@Module({
  providers: [DbWhiteListService],
  exports: [
    DbWhiteListService,
    MongooseModule.forFeature([
      {
        name: DbWhiteList.name,
        schema: DbWhiteListSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbWhiteList.name,
        schema: DbWhiteListSchema,
      },
    ]),
  ],
})
export class DbWhiteListModule {}

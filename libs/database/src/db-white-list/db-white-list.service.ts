import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DbLogService } from '@app/database/db-log/db-log.service';
import {
  DbWhiteList,
  DbWhiteListDocument,
} from '@app/database/db-white-list/db-white-list.model';

@Injectable()
export class DbWhiteListService {
  constructor(
    @InjectModel(DbWhiteList.name)
    private whitelistModel: Model<DbWhiteListDocument>,
    private readonly logger: DbLogService,
  ) {}

  async dbAddWhiteListCoin(userId: any, args, exchange: string) {
    try {
      await this.whitelistModel.findOneAndUpdate(
        {
          userId: userId,
          symbol: args.symbol,
          exchange,
        },
        {
          $set: {
            userId: userId,
            symbol: args.symbol,
            timestamp: new Date(),
            autoAdded: !!args.autoAdded,
            exchange,
          },
        },
        { upsert: true, new: true },
      );

      return {
        ok: true,
        error: null,
      };
    } catch (e) {
      throw Error('Error on saving white list coin');
    }
  }

  async dbGetWhiteListCoins(userId: string, exchange: string) {
    try {
      const result = await this.whitelistModel
        .find({
          userId: userId,
          exchange,
        })
        .sort({ symbol: 1 })
        .lean()
        .exec();

      return result.filter(
        (x) => x.disabled == undefined || x.disabled == false,
      );
    } catch (e) {
      this.logger.errorServer(e.message);
    }

    return null;
  }

  async dbGetAllWhiteListCoins(userId: string, exchange: string) {
    try {
      return this.whitelistModel
        .find({
          userId: userId,
          exchange,
        })
        .sort({ symbol: 1 })
        .lean()
        .exec();
    } catch (e) {
      this.logger.errorServer(e.message);
    }

    return null;
  }

  async dbDeleteWhiteListCoin(
    userId: any,
    args: GQL.IDeleteWhiteListCoinMutationOnMutationArguments,
    exchange: string,
  ) {
    await this.whitelistModel
      .deleteOne({
        userId: userId,
        symbol: args.symbol,
        exchange,
      })
      .exec();

    return {
      ok: true,
      error: null,
    };
  }

  async dbDisableWhiteListCoin(
    args: GQL.IDisableWhiteListCoinMutationOnMutationArguments,
  ) {
    const current = await this.whitelistModel.findById(args.id);

    return this.whitelistModel.findOneAndUpdate(
      {
        _id: current,
      },
      {
        $set: {
          disabled: !current.disabled,
        },
      },
      { new: true },
    );
  }
}

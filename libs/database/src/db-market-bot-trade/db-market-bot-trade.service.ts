import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  DbMarketBotTrade,
  DbMarketBotTradeDocument,
} from '@app/database/db-market-bot-trade/db-market-bot-trade.model';

@Injectable()
export class DbMarketBotTradeService {
  constructor(
    @InjectModel(DbMarketBotTrade.name)
    private marketBotTradeModel: Model<DbMarketBotTradeDocument>,
  ) {}

  async dbGetMarketBotTrades(marketBotId: Types.ObjectId | string) {
    return this.marketBotTradeModel
      .find({
        marketBotId: marketBotId,
      })
      .sort({ tradeDate: 1 })
      .lean()
      .exec();
  }

  async dbGetMarketBotTradesAfterDate(autobotId: string, date: Date) {
    return this.marketBotTradeModel
      .find({
        autobotId: autobotId,
        tradeDate: { $gte: date },
      })
      .sort({ tradeDate: 1 })
      .lean()
      .exec();
  }

  async dbGetLastMarketBotTrade(marketBotId: Types.ObjectId | string) {
    return this.marketBotTradeModel
      .findOne({
        marketBotId: marketBotId,
      })
      .sort({ tradeDate: -1 })
      .lean()
      .exec();
  }

  async dbGetLastLongMarketBotTrade(marketBotId: Types.ObjectId | string) {
    return this.marketBotTradeModel
      .findOne({
        marketBotId: marketBotId,
        position: 'long',
      })
      .sort({ tradeDate: -1 })
      .lean()
      .exec();
  }

  async dbGetMarketBotProfitFrom(autobotId: string, dateFrom) {
    return this.marketBotTradeModel
      .find({
        autobotId: autobotId,
        position: 'short',
        profitUsd: { $ne: null },
        tradeDate: { $gt: dateFrom },
      })
      .sort({ tradeDate: 1 })
      .lean()
      .exec();
  }

  async dbGetMarketBotProfitFromAutobot(autobotId: string) {
    return this.marketBotTradeModel
      .find({
        autobotId: autobotId,
        position: 'short',
        profitUsd: { $ne: null },
      })
      .sort({ tradeDate: 1 })
      .lean()
      .exec();
  }

  async dbGetMarketBotProfitFromAutobotLimit(autobotId: string, limit: number) {
    return this.marketBotTradeModel
      .find({
        autobotId: autobotId,
        // position: 'short',
        // profitUsd: { $ne: null },
      })
      .limit(limit)
      .sort({ tradeDate: -1 })
      .lean()
      .exec();
  }

  async deleteOne(marketBotId: Types.ObjectId | string) {
    await this.marketBotTradeModel.deleteOne({ _id: marketBotId }).exec();
  }

  async dbNewMarketBotTrade(newTrade: DbMarketBotTrade) {
    return new this.marketBotTradeModel(newTrade).save();
  }
}

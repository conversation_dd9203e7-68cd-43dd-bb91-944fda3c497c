import { HydratedDocument } from 'mongoose';
import { <PERSON><PERSON>, <PERSON>hema, SchemaFactory } from '@nestjs/mongoose';

export type DbUserDocument = HydratedDocument<DbUser>;

@Schema({ collection: 'users' })
export class DbUser {
  @Prop()
  username: string;

  @Prop()
  password: string;

  @Prop()
  email: string;

  @Prop()
  confirmed: boolean;

  @Prop({ type: Object })
  notifications: {
    pumpbot: boolean;
    marketBots: boolean;
    signals: any;
  };

  @Prop()
  splitview: [
    {
      market: string;
      interval: string;
      window: number;
    },
  ];

  @Prop({ type: Object })
  trade: {
    alwaysSetStoploss: boolean;
    alwaysSetTP: boolean;
  };

  @Prop()
  exchange: string;
}

export const DbUserSchema = SchemaFactory.createForClass(DbUser);
DbUserSchema.index({ username: 1 });

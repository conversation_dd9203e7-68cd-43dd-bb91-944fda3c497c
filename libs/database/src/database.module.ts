import { Module } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { getMongoConfig } from '../../../configs/mongo.config';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import { DbCommentService } from '@app/database/db-comment/db-comment.service';
import { DbCommentModule } from '@app/database/db-comment/db-comment.module';
import { DbExchangeInfoModule } from '@app/database/db-exchange-info/db-exchange-info.module';
import { DbExcludedSymbolsModule } from '@app/database/db-excluded-symbols/db-excluded-symbols.module';
import { DbCandlesticksModule } from '@app/database/db-candlesticks/db-candlesticks.module';
import { DbMacdModule } from '@app/database/db-macd/db-macd.module';
import { DbMacdService } from '@app/database/db-macd/db-macd.service';
import { DbCandlesticksService } from '@app/database/db-candlesticks/db-candlesticks.service';
import { DbExcludedSymbolsService } from '@app/database/db-excluded-symbols/db-excluded-symbols.service';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import { DbAutoBotService } from '@app/database/db-auto-bot/db-auto-bot.service';
import { DbAutoBotModule } from '@app/database/db-auto-bot/db-auto-bot.module';
import { DbKeyModule } from '@app/database/db-key/db-key.module';
import { DbKeyService } from '@app/database/db-key/db-key.service';
import { DbMarketBotTradeModule } from '@app/database/db-market-bot-trade/db-market-bot-trade.module';
import { DbMarketBotTradeService } from '@app/database/db-market-bot-trade/db-market-bot-trade.service';
import { DbCoinPriceModule } from '@app/database/db-coin-price/db-coin-price.module';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { DbSaldoModule } from '@app/database/db-saldo/db-saldo.module';
import { DbSaldoService } from '@app/database/db-saldo/db-saldo.service';
import { DbMarketBotModule } from '@app/database/db-market-bot/db-market-bot.module';
import { DbUserModule } from '@app/database/db-user/db-user.module';
import { DbUserService } from '@app/database/db-user/db-user.service';
import { DbSaldoUsdModule } from '@app/database/db-saldo-usd/db-saldo-usd.module';
import { DbSaldoUsdService } from '@app/database/db-saldo-usd/db-saldo-usd.service';
import { DbBotOrderModule } from '@app/database/db-bot-order/db-bot-order.module';
import { DbBotOrderService } from '@app/database/db-bot-order/db-bot-order.service';
import { DbTradeModule } from '@app/database/db-trade/db-trade.module';
import { DbTradeService } from '@app/database/db-trade/db-trade.service';
import { DbHiddenSymbolsModule } from '@app/database/db-hidden-symbols/db-hidden-symbols.module';
import { DbHiddenSymbolsService } from '@app/database/db-hidden-symbols/db-hidden-symbols.service';
import { DbWhiteListService } from './db-white-list/db-white-list.service';
import { DbWhiteListModule } from './db-white-list/db-white-list.module';
import { DbMarketBotService } from '@app/database/db-market-bot/db-market-bot.service';
import { DbPumpModule } from './db-pump/db-pump.module';
import { DbPumpService } from '@app/database/db-pump/db-pump.service';
import { DbBotDataService } from './db-bot-data/db-bot-data.service';
import { DbBotDataModule } from './db-bot-data/db-bot-data.module';
import { DbAllTickerService } from './db-all-ticker/db-all-ticker.service';
import { DbAllTickerModule } from './db-all-ticker/db-all-ticker.module';
import { DbFavoriteService } from './db-favorite/db-favorite.service';
import { DbFavoriteModule } from '@app/database/db-favorite/db-favorite.module';
import { DbNoticedCoinService } from './db-noticed-coin/db-noticed-coin.service';
import { DbNoticedCoinModule } from './db-noticed-coin/db-noticed-coin.module';
import { DbStrategyTestModule } from './db-strategy-test/db-strategy-test.module';
import { DbStrategyTestService } from '@app/database/db-strategy-test/db-strategy-test.service';
import { DbStrategyAllTestService } from '@app/database/db-strategy-all-test/db-strategy-all-test.service';
import { DbStrategyAllTestModule } from '@app/database/db-strategy-all-test/db-strategy-all-test.module';
import { DbFearAltIndexService } from './db-fear-alt-index/db-fear-alt-index.service';
import { DbFearAltIndexModule } from './db-fear-alt-index/db-fear-alt-index.module';
import { DbIndicatorService } from './db-indicator/db-indicator.service';
import { DbIndicatorModule } from './db-indicator/db-indicator.module';
import { DbBotProfitsModule } from '@app/database/db-bot-profits/db-bot-profits.module';
import { DbBotProfitsService } from '@app/database/db-bot-profits/db-bot-profits.service';
import { DbCoinIndicatorService } from '@app/database/db-coin-indicator/db-coin-indicator.service';
import { DbCoinIndicatorModule } from '@app/database/db-coin-indicator/db-coin-indicator.module';

@Module({
  providers: [
    DbCommentService,
    DbLogService,
    DbExchangeInfoService,
    DbExcludedSymbolsService,
    DbCandlesticksService,
    DbMacdService,
    DbAutoBotService,
    DbKeyService,
    DbMarketBotTradeService,
    DbCoinPriceService,
    DbSaldoService,
    DbUserService,
    DbSaldoUsdService,
    DbBotOrderService,
    DbTradeService,
    DbHiddenSymbolsService,
    DbWhiteListService,
    DbMarketBotService,
    DbPumpService,
    DbBotDataService,
    DbAllTickerService,
    DbFavoriteService,
    DbNoticedCoinService,
    DbStrategyTestService,
    DbStrategyAllTestService,
    DbFearAltIndexService,
    DbIndicatorService,
    DbBotProfitsService,
    DbCoinIndicatorService,
  ],
  exports: [
    DbCommentService,
    DbLogService,
    DbExchangeInfoService,
    DbExcludedSymbolsService,
    DbCandlesticksService,
    DbMacdService,
    DbAutoBotService,
    DbKeyService,
    DbMarketBotTradeService,
    DbCoinPriceService,
    DbSaldoService,
    DbUserService,
    DbSaldoUsdService,
    DbBotOrderService,
    DbTradeService,
    DbHiddenSymbolsService,
    DbWhiteListService,
    DbMarketBotService,
    DbPumpService,
    DbBotDataService,
    DbAllTickerService,
    DbFavoriteService,
    DbNoticedCoinService,
    DbStrategyTestService,
    DbStrategyAllTestService,
    DbFearAltIndexService,
    DbIndicatorService,
    DbBotProfitsService,
    DbCoinIndicatorService,
  ],
  imports: [
    ConfigModule.forRoot({}),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: getMongoConfig,
    }),
    DbCommentModule,
    DbLogModule,
    DbExchangeInfoModule,
    DbExcludedSymbolsModule,
    DbCandlesticksModule,
    DbMacdModule,
    DbAutoBotModule,
    DbKeyModule,
    DbMarketBotTradeModule,
    DbCoinPriceModule,
    DbSaldoModule,
    DbMarketBotModule,
    DbUserModule,
    DbSaldoUsdModule,
    DbBotOrderModule,
    DbTradeModule,
    DbHiddenSymbolsModule,
    DbWhiteListModule,
    DbPumpModule,
    DbBotDataModule,
    DbAllTickerModule,
    DbFavoriteModule,
    DbNoticedCoinModule,
    DbStrategyTestModule,
    DbStrategyAllTestModule,
    DbFearAltIndexModule,
    DbIndicatorModule,
    DbBotProfitsModule,
    DbCoinIndicatorModule,
  ],
})
export class DatabaseModule {}

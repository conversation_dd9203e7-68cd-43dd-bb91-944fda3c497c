import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbTradeDocument = HydratedDocument<DbTrade>;

@Schema({ collection: 'trades' })
export class DbTrade {
  @Prop()
  user: string;

  @Prop()
  symbol: string;

  @Prop()
  price: number;

  @Prop()
  type: string;

  @Prop()
  quantity: number;

  @Prop({ type: Date })
  timestamp: any;

  @Prop()
  side: string;

  @Prop({ type: Object })
  result: any;

  @Prop()
  exchange: string;
}

export const DbTradeSchema = SchemaFactory.createForClass(DbTrade);
DbTradeSchema.index({ user: 1, symbol: 1, exchange: 1 });

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbMarketBotTradeService } from '@app/database/db-market-bot-trade/db-market-bot-trade.service';
import { equals, last } from 'ramda';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import {
  DbMarketBot,
  DbMarketBotDocument,
} from '@app/database/db-market-bot/db-market-bot.model';
import { DbAutoBotDocument } from '@app/database/db-auto-bot/db-auto-bot.model';
import { DbMarketBotTrade } from '@app/database/db-market-bot-trade/db-market-bot-trade.model';

@Injectable()
export class DbMarketBotService {
  constructor(
    @InjectModel(DbMarketBot.name)
    private marketBotModel: Model<DbMarketBotDocument>,
    private marketBotTradeService: DbMarketBotTradeService,
    private coinPriceService: DbCoinPriceService,
    private readonly logger: DbLogService,
  ) {}

  async dbAddNewMarketBot(
    userId: string,
    data: any | DbMarketBotDocument,
    exchange: string,
  ) {
    const {
      strategy,
      symbol,
      strategyParams,
      interval,
      amountUsdtInvested,
      simulation,
      sellInMinus,
      startWithPosition,
      longPrice,
      limitSell,
      takeProfitPercent,
      fromAutoBot,
      autobotId,
      stoplossPercent,
      btcPrice,
      predictionPrice,
    } = data;

    const newData = {
      user: userId,
      amountUsdtInvested,
      symbol,
      exchange,
      strategy,
      strategyParams,
      interval,
      active: true,
      simulation,
      sellInMinus,
      takeProfitPercent,
      stoplossPercent,
      limitSell,
      startDate: new Date(),
      position: startWithPosition === 'short' ? 'long' : 'short',
      updatedOn: new Date(),
      createdOn: new Date(),
      startPrice: longPrice,
      fromAutoBot,
      autobotId,
      btcPrice,
      predictionPrice,
    };

    const marketBot = await new this.marketBotModel(newData).save();

    if (startWithPosition === 'short') {
      const newTrade = {
        asset: symbol,
        autobotId,
        marketBotId: marketBot._id.toString(),
        position: 'long',
        price: longPrice,
        amount: amountUsdtInvested,
        tradeDate: new Date(),
        btcPrice,
        exchange,
      } as DbMarketBotTrade;

      await this.marketBotTradeService.dbNewMarketBotTrade(newTrade);
    }

    return marketBot;
  }

  async dbGetMarketBotsFromUser(userId: string, exchange: string) {
    return this.marketBotModel
      .find({
        user: userId,
        fromAutoBot: { $ne: true },
        exchange,
      })
      .lean()
      .exec();
  }

  async dbGetManualMarketBots(
    exchange: string,
    symbol: string,
    strategy: string,
  ) {
    const query: any = {
      exchange,
      active: true,
      fromAutoBot: { $ne: true },
      symbol: symbol,
      strategy: strategy,
    };

    const result = this.marketBotModel.find(query).lean().exec();
    return result || [];
  }

  async dbGetMarketBotsBySymbolAndStrategy(
    exchange: string,
    symbol?: string,
    strategy?: string,
  ) {
    const query: any = {
      active: true,
      fromAutoBot: true,
      exchange,
    };

    if (symbol) {
      query.symbol = symbol;
    }

    if (strategy) {
      query.strategy = strategy;
    }

    const result = await this.marketBotModel.find(query).lean().exec();
    return result || [];
  }

  async dbStartStopMarketBot(userId: string, args: any, exchange: string) {
    const { id, action } = args;

    const prevData = await this.marketBotModel
      .findOne({
        _id: id,
        user: userId,
      })
      .lean()
      .exec();

    const currentPrice = await this.coinPriceService.dbGetCoinPrice(
      prevData.symbol,
      exchange,
    );
    const start = action === 'START';
    if (start) {
      return this.marketBotModel
        .findOneAndUpdate(
          {
            user: userId,
            _id: id,
            exchange,
          },
          {
            $set: {
              active: start,
              startPrice: currentPrice,
              startDate: new Date(),
              updatedOn: new Date(),
              exchange,
            },
          },
          { new: true },
        )
        .lean()
        .exec();
    }

    if (!prevData.startPrice) {
      prevData.startPrice = 0;
    }

    const marketPercent = (
      (currentPrice / prevData.startPrice - 1) *
      100
    ).toFixed(2);

    return this.marketBotModel
      .findOneAndUpdate(
        {
          user: userId,
          _id: id,
          exchange,
        },
        {
          $set: {
            active: start,
            endPrice: currentPrice,
            stopDate: new Date(),
            marketPercent,
            updatedOn: new Date(),
            exchange,
          },
        },
        { new: true },
      )
      .lean()
      .exec();
  }

  async dbDeleteMarketBotByIdAndUserId(
    id: string | Types.ObjectId,
    userId: string,
  ) {
    await this.marketBotModel
      .deleteOne({
        user: userId,
        _id: id,
      })
      .exec();
  }

  async dbDeleteMarketBotBySymbol(
    userId: string,
    symbol: string,
    exchange: string,
  ) {
    await this.marketBotModel
      .deleteOne({
        user: userId,
        symbol: symbol,
        exchange,
      })
      .exec();
  }

  async dbAddNewTradeToMarketBot(
    marketBotId: Types.ObjectId | string,
    autobotId: string,
    symbol: string,
    trade: any,
    exchange: string,
  ) {
    if (trade) {
      const newTrade = trade;
      const prevData = await this.marketBotModel
        .findOne({
          _id: marketBotId,
        })
        .lean()
        .exec();

      // Сохраняем действительную цену покупки / продажи в трэйде. Если не можен определить, берем актуальную цену.
      // @ts-ignore
      let lastFillPrice = Number(last(trade.orderResponse.fills).price);

      if (lastFillPrice) {
        newTrade.price = Number(lastFillPrice);
      } else {
        const currentPrice = await this.coinPriceService.dbGetCoinPrice(
          prevData.symbol,
          exchange,
        );
        newTrade.price = currentPrice;
        lastFillPrice = currentPrice;
      }

      const btcPrice: number = await this.coinPriceService.dbGetCoinPrice(
        'BTCUSDT',
        exchange,
      );
      newTrade.valueInUsd = Number(
        Number(lastFillPrice * newTrade.amount).toFixed(2),
      );

      const lastTrade =
        await this.marketBotTradeService.dbGetLastMarketBotTrade(marketBotId);
      if (lastTrade) {
        if (equals(trade.position, 'short')) {
          newTrade.profit = (lastFillPrice / lastTrade.price - 1) * 100;

          const sellValue = lastFillPrice * newTrade.amount;
          newTrade.profitUsd = Number(
            (Number(sellValue) - Number(lastTrade.valueInUsd)).toFixed(2),
          );
        }
      }

      const newSet = {
        lastTrade: new Date(),
        position: newTrade.position === 'long' ? 'short' : 'long',
        updatedOn: new Date(),
      } as any;

      if (newTrade.position === 'long') {
        newSet.startPrice = lastFillPrice;
      }

      newTrade.marketBotId = marketBotId;
      newTrade.autobotId = autobotId;
      newTrade.asset = symbol;
      newTrade.btcPrice = btcPrice;
      newTrade.exchange = exchange;

      // Сохраняем новый трэйд
      await this.marketBotTradeService.dbNewMarketBotTrade(newTrade);

      this.logger.logOrders(symbol, exchange, '-> Market bot trade saved');

      return await this.marketBotModel
        .findOneAndUpdate(
          {
            _id: marketBotId,
          },
          {
            $set: newSet,
          },
          { new: true },
        )
        .lean()
        .exec();
    }
  }

  async dbUpdateMarketBot(args: any, autobot: DbAutoBotDocument) {
    return this.marketBotModel
      .findOneAndUpdate(
        {
          _id: args.id,
        },
        {
          $set: {
            fromAutoBot: true,
            autobotId: autobot._id.toString(),
            exchange: autobot.exchange,
          },
        },
      )
      .lean()
      .exec();
  }

  public async dbGetMarketBotsForAutobot(autobotId: string) {
    return this.marketBotModel
      .find({
        autobotId: autobotId,
      })
      .lean()
      .exec();
  }

  public async dbGetMarketBotForAutobotAndSymbol(
    autobotId: string,
    symbol: string,
  ) {
    return this.marketBotModel
      .findOne({
        autobotId: autobotId,
        symbol: symbol,
      })
      .lean()
      .exec();
  }

  async deleteAllForAutobot(autobotId: string) {
    await this.marketBotModel
      .deleteMany({
        autobotId: autobotId,
      })
      .exec();
  }

  async dbGetMarketBotsFromAutobot(autobot: DbAutoBotDocument) {
    return await this.marketBotModel
      .find({
        autobotId: autobot._id.toString(),
      })
      .lean()
      .exec();
  }
}

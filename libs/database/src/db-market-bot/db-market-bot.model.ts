import { HydratedDocument } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbMarketBotDocument = HydratedDocument<DbMarketBot>;

@Schema({ collection: 'activebots' })
export class DbMarketBot {
  @Prop()
  user: string;

  @Prop()
  symbol: string;

  @Prop()
  exchange: string;

  @Prop()
  strategy: string;

  @Prop()
  startBalanceUSD: number;

  @Prop()
  finalBalanceUSD: number;

  @Prop()
  startBalanceBTC: number;
  @Prop()
  finalBalanceBTC: number;

  @Prop()
  amountUsdtInvested: number;

  @Prop()
  startPrice: number;

  // Start Coin Price
  @Prop()
  predictionPrice: number;

  // Prediction price
  @Prop()
  endPrice: number;

  // End Coin Price
  @Prop()
  marketPercent: number;

  // How much coin developed since start
  @Prop({ type: Date })
  startDate: any; // Bot started on

  @Prop({ type: Date })
  stopDate: any;

  @Prop({ type: Date })
  updatedOn: any; // Updated Bot on

  @Prop({ type: Date })
  createdOn: any;

  @Prop()
  strategyParams: string;

  @Prop()
  interval: string;

  @Prop()
  position: string;

  @Prop()
  profit: number;

  @Prop()
  lastTrade: Date;

  @Prop()
  active: boolean;

  @Prop()
  simulation: boolean;

  @Prop()
  sellInMinus: boolean;

  @Prop()
  limitSell: boolean;

  @Prop()
  takeProfitPercent: number;

  @Prop()
  stoplossPercent: number;

  @Prop()
  fromAutoBot: boolean;

  @Prop()
  autobotId: string;

  currentProfit: number;
  currentValueUsd: number;
  investedUsd: number;
  amount: number; // Amount of buyed coins
}

export const DbMarketBotSchema = SchemaFactory.createForClass(DbMarketBot);
DbMarketBotSchema.index({ user: 1, symbol: 1, exchange: 1 });

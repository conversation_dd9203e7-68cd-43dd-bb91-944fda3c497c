import { Module } from '@nestjs/common';
import { DbMarketBotService } from './db-market-bot.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import { DbMarketBotTradeModule } from '@app/database/db-market-bot-trade/db-market-bot-trade.module';
import { DbCoinPriceModule } from '@app/database/db-coin-price/db-coin-price.module';
import {
  DbMarketBot,
  DbMarketBotSchema,
} from '@app/database/db-market-bot/db-market-bot.model';

@Module({
  providers: [DbMarketBotService],
  exports: [
    DbMarketBotService,
    MongooseModule.forFeature([
      {
        name: DbMarketBot.name,
        schema: DbMarketBotSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    DbMarketBotTradeModule,
    DbCoinPriceModule,
    MongooseModule.forFeature([
      {
        name: DbMarketBot.name,
        schema: DbMarketBotSchema,
      },
    ]),
  ],
})
export class DbMarketBotModule {}

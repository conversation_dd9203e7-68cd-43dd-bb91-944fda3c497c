import { Module } from '@nestjs/common';
import { DbSaldoUsdService } from './db-saldo-usd.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import {
  DbSaldoUsd,
  DbSaldoUsdSchema,
} from '@app/database/db-saldo-usd/db-saldo-usd.model';

@Module({
  providers: [DbSaldoUsdService],
  exports: [
    DbSaldoUsdService,
    MongooseModule.forFeature([
      {
        name: DbSaldoUsd.name,
        schema: DbSaldoUsdSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbSaldoUsd.name,
        schema: DbSaldoUsdSchema,
      },
    ]),
  ],
})
export class DbSaldoUsdModule {}

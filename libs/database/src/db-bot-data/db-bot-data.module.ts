import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import { DbBotDataService } from '@app/database/db-bot-data/db-bot-data.service';
import {
  DbBotData,
  DbBotDataSchema,
} from '@app/database/db-bot-data/db-bot-data.model';

@Module({
  providers: [DbBotDataService],
  exports: [
    DbBotDataService,
    MongooseModule.forFeature([
      {
        name: DbBotData.name,
        schema: DbBotDataSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbBotData.name,
        schema: DbBotDataSchema,
      },
    ]),
  ],
})
export class DbBotDataModule {}

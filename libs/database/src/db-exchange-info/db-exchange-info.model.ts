import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbExchangeInfoDocument = HydratedDocument<DbExchangeInfo>;

@Schema({ collection: 'exchangeinfos' })
export class DbExchangeInfo {
  @Prop()
  symbol: string;

  @Prop({ type: Object })
  symbolData: any;

  @Prop({ type: Date })
  timestamp: any;

  @Prop()
  exchange: string;
}

export const DbExchangeInfoSchema =
  SchemaFactory.createForClass(DbExchangeInfo);
DbExchangeInfoSchema.index({ symbol: 1, exchange: 1 });
DbExchangeInfoSchema.index({ exchange: 1 });

import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbAllTickerDocument = HydratedDocument<DbAllTicker>;

@Schema({ collection: 'alltickers' })
export class DbAllTicker {
  @Prop()
  symbol: string;

  @Prop({ type: Object })
  data: any;

  @Prop({ type: Date })
  timestamp: any;

  @Prop()
  exchange: string;
}

export const DbAllTickerSchema = SchemaFactory.createForClass(DbAllTicker);
DbAllTickerSchema.index({ symbol: 1, timestamp: 1, exchange: 1 });
DbAllTickerSchema.index({ symbol: 1, exchange: 1 });

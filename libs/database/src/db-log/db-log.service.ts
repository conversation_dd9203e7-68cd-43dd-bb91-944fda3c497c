import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DatabaseModule } from '@app/database';
import { DbLog, DbLogDocument } from '@app/database/db-log/db-log.model';

@Injectable()
export class DbLogService {
  private readonly logger = new Logger(DatabaseModule.name);

  constructor(
    @InjectModel(DbLog.name) private readonly dblog: Model<DbLogDocument>,
  ) {}

  dbGetLogs(limit = 500, service: string, level: string) {
    const query: any = {};
    if (service) {
      query.service = service;
    }

    if (level) {
      query.level = level;
    }
    return this.dblog
      .find(query)
      .sort({ timestamp: -1 })
      .limit(limit)
      .lean()
      .exec();
  }

  log(service: string, ...msg: (string | number)[]) {
    try {
      const joined = msg.join(' ');
      this.logger.log(joined);

      return new this.dblog({
        msg: joined,
        service,
        level: 'log',
        timestamp: new Date(),
      }).save();
    } catch (e) {
      this.errorServer(e);
    }
  }

  warn(service: string, ...msg: (string | number)[]) {
    try {
      const joined = msg.join(' ');
      this.logger.warn(joined);

      return new this.dblog({
        msg: joined,
        service,
        level: 'warn',
        timestamp: new Date(),
      }).save();
    } catch (e) {
      this.errorServer(e);
    }
  }

  error(service: string, ...msg: (string | number)[]) {
    try {
      const joined = msg.map((x) => JSON.stringify(x)).join(' ');
      this.logger.error(joined);

      return new this.dblog({
        msg: joined,
        service,
        level: 'error',
        timestamp: new Date(),
      }).save();
    } catch (e) {
      this.errorServer(e);
    }
  }

  async getLog() {
    try {
      return this.dblog
        .find({
          level: 'log',
        })
        .sort({
          timestamp: -1,
        })
        .lean()
        .limit(999)
        .exec();
    } catch (e) {
      this.errorServer(e);
    }
  }

  async getWarn() {
    try {
      return this.dblog
        .find({
          level: 'warn',
        })
        .sort({
          timestamp: -1,
        })
        .lean()
        .limit(999)
        .exec();
    } catch (e) {
      this.errorServer(e);
    }
  }

  async getError() {
    try {
      return this.dblog
        .find({
          level: 'error',
        })
        .sort({
          timestamp: -1,
        })
        .lean()
        .limit(999)
        .exec();
    } catch (e) {
      this.errorServer(e);
    }
  }

  errorIndicators(...msg: (string | number)[]) {
    this.error('mb-indicators', ...msg);
  }

  errorTester(...msg: (string | number)[]) {
    this.error('mb-strategy', ...msg);
  }

  errorServer(...msg: (string | number)[]) {
    this.error('mb-server', ...msg);
  }

  errorReports(...msg: (string | number)[]) {
    this.error('mb-reports', ...msg);
  }

  errorPump(...msg: (string | number)[]) {
    this.error('mb-pump', ...msg);
  }

  errorOrders(...msg: (string | number)[]) {
    this.error('mb-orders', ...msg);
  }

  errorCandles(...msg: (string | number)[]) {
    this.error('mb-candles', ...msg);
  }

  errorBot(...msg: (string | number)[]) {
    this.error('mb-bot', ...msg);
  }

  warnStrategy(...msg: (string | number)[]) {
    this.warn('mb-strategy', ...msg);
  }

  logIndicators(...msg: (string | number)[]) {
    this.log('mb-indicators', ...msg);
  }

  logBot(...msg: (string | number)[]) {
    this.log('mb-bot', ...msg);
  }

  logCandles(...msg: (string | number)[]) {
    this.log('mb-candles', ...msg);
  }

  logOrders(...msg: (string | number)[]) {
    this.log('mb-orders', ...msg);
  }

  logPump(...msg: (string | number)[]) {
    this.log('mb-pump', ...msg);
  }

  logServer(...msg: (string | number)[]) {
    this.log('mb-server', ...msg);
  }

  logTester(...msg: (string | number)[]) {
    this.log('mb-strategy', ...msg);
  }

  logReports(...msg: (string | number)[]) {
    this.log('mb-reports', ...msg);
  }

  warnBot(...msg: (string | number)[]) {
    this.warn('mb-bot', ...msg);
  }

  warnCandles(...msg: (string | number)[]) {
    this.warn('mb-candles', ...msg);
  }

  warnOrders(...msg: (string | number)[]) {
    this.warn('mb-orders', ...msg);
  }

  warnPump(...msg: (string | number)[]) {
    this.warn('mb-pump', ...msg);
  }

  warnServer(...msg: (string | number)[]) {
    this.warn('mb-server', ...msg);
  }
}

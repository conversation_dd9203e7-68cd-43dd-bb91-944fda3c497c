import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbLog, DbLogSchema } from '@app/database/db-log/db-log.model';

@Module({
  providers: [DbLogService],
  exports: [
    DbLogService,
    MongooseModule.forFeature([
      {
        name: DbLog.name,
        schema: DbLogSchema,
      },
    ]),
  ],
  imports: [
    MongooseModule.forFeature([
      {
        name: DbLog.name,
        schema: DbLogSchema,
      },
    ]),
  ],
})
export class DbLogModule {}

import { HydratedDocument } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbBotProfitsDocument = HydratedDocument<DbBotProfits>;

@Schema({ collection: 'botprofits' })
export class DbBotProfits {
  @Prop()
  symbol: string;

  @Prop()
  strategy: string;

  @Prop()
  interval: string;

  @Prop()
  profit: number;

  @Prop()
  tradesPos: number;

  @Prop()
  tradesNeg: number;

  @Prop({ type: Date })
  profitForDay: any;

  @Prop({ type: Date })
  updateTimestamp: any;

  @Prop()
  exchange: string;
}

export const DbBotProfitsSchema = SchemaFactory.createForClass(DbBotProfits);
DbBotProfitsSchema.index({ symbol: 1, strategy: 1, interval: 1, exchange: 1 });
DbBotProfitsSchema.index({
  symbol: 1,
  strategy: 1,
  interval: 1,
  profitForDay: 1,
  exchange: 1,
});

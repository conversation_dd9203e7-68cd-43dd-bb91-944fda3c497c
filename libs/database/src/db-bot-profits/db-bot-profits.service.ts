import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { Model } from 'mongoose';
import {
  DbBotProfits,
  DbBotProfitsDocument,
} from '@app/database/db-bot-profits/db-bot-profits.model';

@Injectable()
export class DbBotProfitsService {
  constructor(
    @InjectModel(DbBotProfits.name)
    private botProfitsModel: Model<DbBotProfitsDocument>,
  ) {}

  public async dbSetProfit(
    symbol: string,
    strategy: string,
    interval: string,
    exchange: string,
    profit: number,
    tradesNeg: number,
    tradesPos: number,
    profitForDay?: Date,
  ) {
    const query: any = {
      symbol,
      strategy,
      interval,
      exchange,
    };

    if (profitForDay) {
      query.profitForDay = profitForDay;
    }

    await this.botProfitsModel
      .findOneAndUpdate(
        query,
        {
          $set: {
            profit,
            tradesNeg,
            tradesPos,
            profitForDay,
            exchange,
            updateTimestamp: new Date(),
          },
        },
        { upsert: true },
      )
      .lean()
      .exec();
  }

  public async dbGetTotalProfitForStrategies(
    exchange: string,
  ): Promise<DbBotProfitsDocument[]> {
    return this.botProfitsModel
      .find({
        symbol: 'ALL',
        profitForDay: null,
        exchange,
      })
      .lean()
      .exec();
  }

  public async dbGetProfit(
    symbol: string,
    strategy: string,
    interval: string,
    exchange: string,
  ): Promise<DbBotProfitsDocument> {
    if (symbol == null || symbol == '') {
      symbol = 'ALL';
    }

    return this.botProfitsModel
      .findOne({
        symbol,
        strategy,
        interval,
        exchange,
        profitForDay: null,
      })
      .lean()
      .exec();
  }

  public async dbGetAllStrategyProfitsFromDate(fromDate: Date): Promise<
    {
      _id: {
        strategy: string;
        interval: string;
      };
      total: number;
    }[]
  > {
    const query: any = {
      profitForDay: { $gte: new Date(fromDate) },
    };

    const $group = {
      _id: { strategy: '$strategy', interval: '$interval' },
      total: { $sum: '$profit' },
    };

    return await this.botProfitsModel
      .aggregate([{ $match: query }, { $group: $group }])
      .exec();
  }

  public async dbGetStrategyProfitsFromDate(
    fromDate: Date,
    strategy: string,
    interval: string,
    exchange: string,
  ): Promise<
    {
      _id: {
        profitForDay: string;
      };
      total: number;
    }[]
  > {
    const query: any = {
      strategy,
      interval,
      exchange,
      profitForDay: { $gte: new Date(fromDate) },
    };

    const $group = {
      _id: { profitForDay: '$profitForDay' },
      total: { $sum: '$profit' },
    };

    return await this.botProfitsModel
      .aggregate([{ $match: query }, { $group: $group }])
      .sort({ '_id.profitForDay': 1 })
      .exec();
  }

  public async dbGetStrategyProfitsFromDateToDate(
    fromDate: Date,
    toDate: Date,
    strategy: string,
    interval: string,
    exchange: string,
  ): Promise<
    {
      _id: {
        profitForDay: string;
      };
      total: number;
    }[]
  > {
    const query: any = {
      strategy,
      interval,
      exchange,
      profitForDay: { $gte: new Date(fromDate), $lt: new Date(toDate) },
    };

    const $group = {
      _id: { profitForDay: '$profitForDay' },
      total: { $sum: '$profit' },
    };

    return await this.botProfitsModel
      .aggregate([{ $match: query }, { $group: $group }])
      .sort({ '_id.profitForDay': 1 })
      .exec();
  }

  public async dbGetDailyProfit(
    symbol: string,
    strategy: string,
    interval: string,
    profitForDay: Date,
    exchange: string,
  ): Promise<DbBotProfitsDocument> {
    if (symbol == null || symbol == '') {
      symbol = 'ALL';
    }

    return this.botProfitsModel
      .findOne({
        symbol,
        strategy,
        interval,
        profitForDay,
        exchange,
      })
      .lean()
      .exec();
  }
}

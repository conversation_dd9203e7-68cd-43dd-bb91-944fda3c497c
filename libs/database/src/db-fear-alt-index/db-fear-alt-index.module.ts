import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DbFearAltIndexService } from '@app/database/db-fear-alt-index/db-fear-alt-index.service';
import {
  DbFearAltIndex,
  DbFearAltIndexSchema,
} from '@app/database/db-fear-alt-index/db-fear-alt-index.model';

@Module({
  providers: [DbFearAltIndexService],
  exports: [
    DbFearAltIndexService,
    MongooseModule.forFeature([
      {
        name: DbFearAltIndex.name,
        schema: DbFearAltIndexSchema,
      },
    ]),
  ],
  imports: [
    MongooseModule.forFeature([
      {
        name: DbFearAltIndex.name,
        schema: DbFearAltIndexSchema,
      },
    ]),
  ],
})
export class DbFearAltIndexModule {}

import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbFavoriteDocument = HydratedDocument<DbFavorite>;

@Schema({ collection: 'favoritecoins' })
export class DbFavorite {
  @Prop()
  userId: string;

  @Prop()
  symbol: string;

  @Prop()
  exchange: string;
}

export const DbFavoriteSchema = SchemaFactory.createForClass(DbFavorite);

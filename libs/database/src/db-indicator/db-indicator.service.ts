import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { Model, Types } from 'mongoose';
import {
  DbIndicator,
  DbIndicatorDocument,
} from '@app/database/db-indicator/db-indicator.model';
import { addDay } from '@formkit/tempo';
import PriceCalculation from '../../../../utils/PriceCalculation';

@Injectable()
export class DbIndicatorService {
  constructor(
    @InjectModel(DbIndicator.name)
    private indicatorDocumentModel: Model<DbIndicatorDocument>,
  ) {}

  public async getAllIndicators(size: number, exchange: string) {
    return this.indicatorDocumentModel
      .find({
        exchange,
      })
      .limit(size)
      .sort({ candleTime: -1 })
      .lean()
      .exec();
  }

  public async getAllIndicatorsForSymbolAndStrategyAndInterval(
    symbol: string,
    strategy: string,
    interval: string,
    exchange: string,
  ) {
    return this.indicatorDocumentModel
      .find({
        symbol,
        indicator: strategy,
        interval,
        exchange,
      })
      .sort({ candleTime: 1 })
      .lean()
      .exec();
  }

  public async getProfitsForSymbolAndInterval(
    symbol: string,
    strategy: string,
    interval: string,
    exchange: string,
  ): Promise<{ total: number; tradesNeg: number; tradesPos: number }> {
    const query = {
      symbol,
      indicator: strategy,
      interval,
      profit: { $ne: null },
      action: 'short',
      exchange,
    };

    const result = await this.indicatorDocumentModel
      .find(query)
      .sort({ candleTime: 1 })
      .lean()
      .exec();

    let totalUsd = 1000;
    let tradesPos = 0;
    let tradesNeg = 0;

    for (const curTrade of result) {
      if (curTrade.profit != 0) {
        totalUsd = totalUsd * (1 + curTrade.profit / 100);

        if (curTrade.profit > 0) {
          tradesPos++;
        } else if (curTrade.profit < 0) {
          tradesNeg++;
        }
      }
    }

    const total = Number(PriceCalculation.toPercentGain(1000, totalUsd));

    return {
      total,
      tradesPos,
      tradesNeg,
    };
  }

  public async calculateProfitsForDay(
    strategy: string,
    interval: string,
    exchange: string,
    symbol: string,
    fromDate: Date,
    toDate: Date,
  ): Promise<{ total: number; tradesNeg: number; tradesPos: number }> {
    const query: any = {
      indicator: strategy,
      interval,
      exchange,
      symbol,
      action: 'short',
      candleTime: { $gte: new Date(fromDate), $lte: new Date(toDate) },
      profit: { $ne: null },
    };

    const $group = {
      _id: '$action',
      total: { $sum: '$profit' },
    };

    const result = await this.indicatorDocumentModel
      .aggregate([{ $match: query }, { $group: $group }])
      .limit(1)
      .exec();

    query.profit = { $gte: 0 };
    const tradesPos = await this.indicatorDocumentModel.countDocuments(query);

    query.profit = { $lt: 0 };
    const tradesNeg = await this.indicatorDocumentModel.countDocuments(query);

    if (result.length > 0) {
      return {
        total: result.length > 0 ? result[0].total : 0,
        tradesPos,
        tradesNeg,
      };
    }

    return {
      total: 0,
      tradesPos,
      tradesNeg,
    };
  }

  public async getIndicatorsForSymbol(
    symbol: string,
    indicator: string,
    interval: string,
    exchange: string,
  ) {
    const indicators = [];
    if (indicator != null) {
      indicators.push(indicator);
    } else {
      const r = await this.indicatorDocumentModel.distinct('indicator');
      indicators.push(r);
    }

    const intervals = [];
    if (interval != null) {
      intervals.push(indicator);
    } else {
      const r = await this.indicatorDocumentModel.distinct('interval');
      intervals.push(r);
    }

    const results = [];

    for (const interval of intervals) {
      for (const indicator of indicators) {
        const result = await this.indicatorDocumentModel
          .find({
            symbol,
            interval,
            indicator,
            exchange,
          })
          .sort({ candleTime: -1 })
          .limit(1)
          .lean()
          .exec();

        if (result.length > 0) {
          results.push(result[0]);
        }
      }
    }

    return results;
  }

  public async getIndicatorsForSymbolAndStrategy(
    symbol: string,
    strategy: string,
    interval: string,
    exchange: string,
    fromDate: Date,
    limit: number,
  ): Promise<DbIndicatorDocument[]> {
    const query: any = {
      interval,
      indicator: strategy,
      exchange,
    };

    if (fromDate) {
      query.candleTime = { $gte: fromDate };
    }

    if (symbol) {
      query.symbol = new RegExp('^' + symbol.toUpperCase(), 'i');
    }

    return this.indicatorDocumentModel
      .find(query)
      .sort({ candleTime: -1 })
      .limit(limit ? limit : 500)
      .lean()
      .exec();
  }

  async getAllMarketSignalsFromIndicators(
    indicator: string,
    interval: string,
    symbols: string[],
    exchange: string,
  ): Promise<DbIndicatorDocument[]> {
    const resultSet = [];

    for (const symbol of symbols.sort()) {
      try {
        const result = await this.indicatorDocumentModel
          .find({
            symbol,
            interval,
            indicator,
            exchange,
          })
          .sort({
            candleTime: -1,
          })
          .limit(1)
          .lean()
          .exec();

        if (result.length > 0) {
          resultSet.push(result[0]);
        }
      } catch (e) {
        console.error(symbol, e);
      }
    }

    return resultSet;
  }

  async getLastIndicatorAction(
    symbol: string,
    interval: string,
    indicator: string,
    exchange: string,
    action?: string,
  ): Promise<DbIndicatorDocument> {
    const query: any = {
      symbol,
      interval,
      indicator,
      exchange,
    };

    if (action) {
      query.action = action;
    }

    const result = await this.indicatorDocumentModel
      .find(query)
      .sort({
        candleTime: -1,
      })
      .limit(1)
      .lean()
      .exec();

    if (result.length > 0) {
      return result[0];
    }

    return null;
  }

  public async addIndicator(data: DbIndicator) {
    return new this.indicatorDocumentModel(data).save();
  }

  public async getAllSignalsFromIndicators(
    args: GQL.ISignalsOnBotQueryArguments,
    exchange: string,
  ): Promise<DbIndicatorDocument[]> {
    const { strategy, symbol, interval, days, limit } = args;

    const now = new Date();
    const fromDate = days ? addDay(now, -days) : undefined;

    const result = await this.getIndicatorsForSymbolAndStrategy(
      symbol,
      strategy,
      interval,
      exchange,
      fromDate,
      limit,
    );

    return result.map((x) => {
      return {
        ...x,
        advice: x.action,
        strategy: x.indicator,
        currentDiff: x.profit,
      } as any;
    });
  }

  public async updateProfit(id: Types.ObjectId, profit: number) {
    await this.indicatorDocumentModel
      .findOneAndUpdate(
        {
          _id: id,
        },
        {
          $set: {
            profit,
          },
        },
      )
      .lean()
      .exec();
  }

  public async getNewIndicators() {
    return this.indicatorDocumentModel
      .find({
        newAdded: true,
      })
      .sort({
        timestamp: 1,
      })
      .lean()
      .exec();
  }

  public async setNewIndicatorsToFalse(id: Types.ObjectId) {
    await this.indicatorDocumentModel
      .findOneAndUpdate(
        {
          _id: id,
        },
        {
          $set: {
            newAdded: false,
          },
        },
      )
      .lean()
      .exec();
  }
}

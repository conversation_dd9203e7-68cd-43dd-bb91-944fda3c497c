import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  DbStrategyAllTest,
  DbStrategyAllTestDocument,
} from '@app/database/db-strategy-all-test/db-strategy-all-test.model';

@Injectable()
export class DbStrategyAllTestService {
  constructor(
    @InjectModel(DbStrategyAllTest.name)
    private strategyAllTestDocumentModel: Model<DbStrategyAllTestDocument>,
  ) {}

  dbGetAllStrategyTests = async (userId: string, exchange: string) => {
    const strategies = await this.strategyAllTestDocumentModel
      .distinct('strategy')
      .sort();

    const intervals =
      await this.strategyAllTestDocumentModel.distinct('interval');

    const result: DbStrategyAllTestDocument[] = [];

    for (const interval of intervals) {
      for (const strategy of strategies) {
        const q: any = {
          userId: userId,
          strategy: strategy,
          interval: interval,
          exchange,
        };

        const res = await this.strategyAllTestDocumentModel
          .find(q)
          .sort({
            createTimestamp: -1,
          })
          .limit(1)
          .lean()
          .exec();

        if (res.length != 0) {
          result.push(res[0]);
        }
      }
    }

    return result;
  };

  async dbUpdateAllStrategy(
    id: Types.ObjectId,
    args: {
      profit: number;
      profitUsd: number;
      worstSymbolProfit: number;
      bestSymbolProfit: number;
      avgProfit: number;
      bestPerformer: string;
      worstPerformer: string;
      numTrades: number;
      posTrades: number;
      negTrades: number;
      currentSymbol: string;
    },
  ) {
    const {
      bestSymbolProfit,
      worstSymbolProfit,
      profitUsd,
      profit,
      avgProfit,
      bestPerformer,
      worstPerformer,
      numTrades,
      posTrades,
      negTrades,
      currentSymbol,
    } = args;
    await this.strategyAllTestDocumentModel
      .findOneAndUpdate(
        {
          _id: id,
        },
        {
          $set: {
            profit: profit,
            profitUsd: profitUsd,
            bestSymbolProfit: bestSymbolProfit,
            worstSymbolProfit: worstSymbolProfit,
            bestPerformer: bestPerformer,
            worstPerformer: worstPerformer,
            updateTimestamp: new Date(),
            avgProfit: avgProfit,
            numTrades: numTrades,
            posTrades: posTrades,
            negTrades: negTrades,
            currentSymbol: currentSymbol,
          },
        },
      )
      .lean()
      .exec();
  }

  async dbUpdateStrategyTestStatus(id: Types.ObjectId, status: string) {
    await this.strategyAllTestDocumentModel
      .findOneAndUpdate(
        {
          _id: id,
        },
        {
          $set: {
            status: status,
            updateTimestamp: new Date(),
            currentSymbol: null,
          },
        },
      )
      .lean()
      .exec();
  }

  async dbCreateStrategyTest(args: {
    symbols;
    userId: string;
    strategy: string;
    interval: string;
    startDate;
    endDate;
    invest;
    exchange: string;
    sl?;
  }) {
    const {
      symbols,
      userId,
      strategy,
      interval,
      startDate,
      endDate,
      invest,
      sl,
      exchange,
    } = args;

    try {
      return await new this.strategyAllTestDocumentModel({
        strategy,
        interval,
        userId: userId,
        invest,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        status: 'New',
        progressStrategy: 0,
        progressInterval: 0,
        progressSymbol: 0,
        totalProgressSymbol: 0,
        numShorts: 0,
        investPerCoin: invest,
        bestSymbolProfit: 0,
        worstSymbolProfit: 0,
        avgProfit: 0,
        avgHoldMinutes: 0,
        avgTradesDay: 0,
        bestTrade: 0,
        avgTrade: 0,
        worstTrade: 0,
        createTimestamp: new Date(),
        sl,
        symbols,
        exchange,
      }).save();
    } catch (e) {
      throw e;
    }
  }
}

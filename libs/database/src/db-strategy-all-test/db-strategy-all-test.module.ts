import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DbStrategyAllTestService } from '@app/database/db-strategy-all-test/db-strategy-all-test.service';
import {
  DbStrategyAllTest,
  DbStrategyAllTestSchema,
} from '@app/database/db-strategy-all-test/db-strategy-all-test.model';

@Module({
  providers: [DbStrategyAllTestService],
  exports: [
    DbStrategyAllTestService,
    MongooseModule.forFeature([
      {
        name: DbStrategyAllTest.name,
        schema: DbStrategyAllTestSchema,
      },
    ]),
  ],
  imports: [
    MongooseModule.forFeature([
      {
        name: DbStrategyAllTest.name,
        schema: DbStrategyAllTestSchema,
      },
    ]),
  ],
})
export class DbStrategyAllTestModule {}

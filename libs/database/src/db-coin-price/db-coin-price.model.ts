import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbCoinPriceDocument = HydratedDocument<DbCoinPrice>;

@Schema({ collection: 'currentcoinprices' })
export class DbCoinPrice {
  @Prop({ type: String, required: true })
  symbol: string;

  @Prop()
  price: number;

  @Prop({ type: Date })
  timestamp: any;

  @Prop()
  exchange: string;
}

export const DbCoinPriceSchema = SchemaFactory.createForClass(DbCoinPrice);
DbCoinPriceSchema.index({ symbol: 1, exchange: 1 });
DbCoinPriceSchema.index({ exchange: 1 });

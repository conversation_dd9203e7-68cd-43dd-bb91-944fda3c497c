import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DbCoinIndicatorService } from '@app/database/db-coin-indicator/db-coin-indicator.service';
import {
  DbCoinIndicator,
  DbCoinIndicatorSchema,
} from '@app/database/db-coin-indicator/db-coin-indicator.model';

@Module({
  providers: [DbCoinIndicatorService],
  exports: [
    DbCoinIndicatorService,
    MongooseModule.forFeature([
      {
        name: DbCoinIndicator.name,
        schema: DbCoinIndicatorSchema,
      },
    ]),
  ],
  imports: [
    MongooseModule.forFeature([
      {
        name: DbCoinIndicator.name,
        schema: DbCoinIndicatorSchema,
      },
    ]),
  ],
})
export class DbCoinIndicatorModule {}

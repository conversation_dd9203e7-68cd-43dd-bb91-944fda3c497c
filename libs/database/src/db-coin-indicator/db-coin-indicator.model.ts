import { HydratedDocument } from 'mongoose';
import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';

export type DbCoinIndicatorDocument = HydratedDocument<DbCoinIndicator>;

@Schema({ collection: 'coinindicators' })
export class DbCoinIndicator {
  @Prop()
  symbol: string;

  @Prop({ type: Date })
  updateTimestamp: any;

  @Prop()
  interval: string;

  @Prop()
  ao: number;

  @Prop()
  aoTrend: string; // up or down

  @Prop()
  adx: number;

  @Prop()
  rsi: number;

  @Prop()
  btcAO1d: number;

  @Prop()
  atr: number;

  @Prop()
  ema25: number;

  @Prop()
  ema50: number;

  @Prop()
  ema100: number;

  @Prop()
  mfi: number;

  @Prop()
  vwap: number;

  @Prop({ type: Object })
  ichimoku: any;

  @Prop()
  roc: number;

  @Prop()
  kc_upper: number;

  @Prop()
  kc_middle: number;

  @Prop()
  doncian_upper: number;

  @Prop()
  doncian_lower: number;

  @Prop()
  bb_upper: number;

  @Prop()
  bb_middle: number;

  @Prop()
  bb_lower: number;

  @Prop()
  bb_obv: number;

  @Prop()
  ad: number;

  @Prop()
  so_k: number;

  @Prop()
  so_d: number;

  @Prop()
  sma7: number;

  @Prop()
  sma21: number;

  @Prop()
  sma100: number;

  @Prop()
  macd: number;

  @Prop()
  price: number;

  @Prop()
  nesterov: string;

  @Prop()
  exchange: string;
}

export const DbCoinIndicatorSchema =
  SchemaFactory.createForClass(DbCoinIndicator);
DbCoinIndicatorSchema.index({ symbol: 1, exchange: 1 });
DbCoinIndicatorSchema.index({ symbol: 1, interval: 1, exchange: 1 });

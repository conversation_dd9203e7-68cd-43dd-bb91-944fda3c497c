import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  DbExcludedSymbols,
  DbExcludedSymbolsDocument,
} from '@app/database/db-excluded-symbols/db-excluded-symbols.model';

@Injectable()
export class DbExcludedSymbolsService {
  constructor(
    @InjectModel(DbExcludedSymbols.name)
    private excludedSymbolsModel: Model<DbExcludedSymbolsDocument>,
  ) {}

  addExcludedSymbol = async (symbol: string) => {
    return new this.excludedSymbolsModel({
      symbol,
    }).save();
  };

  dbGetExcludedSymbols = async (exchange: string) => {
    const result = await this.excludedSymbolsModel
      .find({
        exchange,
      })
      .lean()
      .exec();

    if (result) {
      return result.map((data) => data.symbol);
    }

    return [];
  };
}

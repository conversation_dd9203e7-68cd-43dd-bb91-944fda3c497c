import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { BybitConnectService } from '@app/bybit-api/bybit-connect/bybit-connect.service';
import { GetInstrumentsInfoParamsV5 } from 'bybit-api/lib/types';

@Injectable()
export class BybitExchangeInfoService {
  constructor(
    private logger: DbLogService,
    private connect: BybitConnectService,
  ) {}

  async getExchangeInfo() {
    const client = this.connect.bybitRestInit();
    try {
      const params: GetInstrumentsInfoParamsV5 = {
        category: 'spot',
      };
      return await client.getInstrumentsInfo(params);
    } catch (e) {
      this.logger.errorServer(e);
    }

    return null;
  }
}

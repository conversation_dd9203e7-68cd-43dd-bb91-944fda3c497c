import { Module } from '@nestjs/common';
import { DatabaseModule } from '@app/database';
import { BybitConnectService } from './bybit-connect/bybit-connect.service';
import { BybitExchangeInfoService } from './bybit-exchange-info/bybit-exchange-info.service';

@Module({
  providers: [BybitConnectService, BybitExchangeInfoService],
  exports: [BybitConnectService, BybitExchangeInfoService],
  imports: [DatabaseModule],
})
export class BybitApiModule {}

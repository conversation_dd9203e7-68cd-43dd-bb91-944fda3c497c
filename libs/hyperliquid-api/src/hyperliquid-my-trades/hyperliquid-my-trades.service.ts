import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { HyperliquidConnectService } from '@app/hyperliquid-api/hyperliquid-connect/hyperliquid-connect.service';
import { Db<PERSON><PERSON> } from '@app/database/db-key/db-key.model';
import * as hl from '@nktkas/hyperliquid';

interface MyTradesArgs {
  symbol: string;
  limit?: number;
  fromId?: number;
  recvWindow?: number;
}

interface MyTrade {
  symbol: string;
  id: number;
  orderId: number;
  orderListId: number;
  price: string;
  qty: string;
  quoteQty: string;
  commission: string;
  commissionAsset: string;
  time: number;
  isBuyer: boolean;
  isMaker: boolean;
  isBestMatch: boolean;
}

@Injectable()
export class HyperliquidMyTradesService {
  constructor(
    private logger: DbLogService,
    private hyperliquidConnectService: HyperliquidConnectService,
  ) {}

  async getMyTrades(
    apiKey: <PERSON>b<PERSON><PERSON>,
    args: MyTradesArgs,
  ): Promise<MyTrade[]> {
    try {
      const infoClient = this.hyperliquidConnectService.initInfoClient();
      
      // Convert symbol format (BTCUSDT -> BTC)
      const coin = args.symbol.replace('USDT', '');
      
      const userFills = await infoClient.userFills({
        user: apiKey.key, // In Hyperliquid, the key is the wallet address
      });

      if (!userFills || !Array.isArray(userFills)) {
        return [];
      }

      // Filter by coin and apply limit
      let filteredFills = userFills.filter(fill => fill.coin === coin);
      
      if (args.fromId) {
        filteredFills = filteredFills.filter(fill => fill.tid >= args.fromId);
      }

      if (args.limit) {
        filteredFills = filteredFills.slice(0, args.limit);
      }

      // Convert to Binance-compatible format
      return filteredFills.map(fill => {
        const symbol = `${fill.coin}USDT`;
        const price = parseFloat(fill.px);
        const qty = Math.abs(parseFloat(fill.sz));
        const quoteQty = price * qty;
        
        return {
          symbol,
          id: fill.tid,
          orderId: fill.oid,
          orderListId: -1,
          price: fill.px,
          qty: qty.toString(),
          quoteQty: quoteQty.toString(),
          commission: fill.fee || '0',
          commissionAsset: 'USDC', // Hyperliquid uses USDC for fees
          time: fill.time,
          isBuyer: parseFloat(fill.sz) > 0,
          isMaker: !fill.crossed, // If not crossed, it was a maker order
          isBestMatch: true,
        } as MyTrade;
      });

    } catch (e) {
      this.logger.errorServer('Error fetching Hyperliquid trades', e);
      return [];
    }
  }
}

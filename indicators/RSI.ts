import { last, pluck, reverse } from 'ramda';
import { RSI } from 'technicalindicators';
import { DbCandleStick } from '@app/database/db-candlesticks/db-candlesticks.model';
import { RSIInput } from 'technicalindicators/declarations/generated';
import { calcAORangeFromTimestamp } from './AO';

const calcRSI = (openValues: number[], period: number): number[] => {
  const input: RSIInput = {
    values: openValues,
    period: period,
  };

  return RSI.calculate(input);
};

export const calcRSIFromCandles = (
  candles: DbCandleStick[],
  period: number = 14,
): {
  rsi: number;
  openTime: number;
  coinPriceUsd: number;
}[] => {
  const open = pluck('open')(candles);

  const resultValues = calcRSI(open, period);
  const result = [];
  const candlesReverse = reverse(candles);
  const resultReverse = reverse(resultValues);

  resultReverse.forEach((value, idx) => {
    result.push({
      rsi: value,
      openTime: candlesReverse.at(idx).openTime,
      coinPriceUsd: candlesReverse.at(idx).open,
    });
  });

  return reverse(result);
};

export const calcRSIFromTimestamp = (
  openTime: number,
  candles: any[],
): number => {
  const output = calcRSIRangeFromTimestamp(openTime, candles);
  return last(output).rsi;
};

export const calcRSIRangeFromTimestamp = (
  openTime: number,
  candles: DbCandleStick[],
  period: number = 14,
): {
  rsi: number;
  openTime: number;
  coinPriceUsd: number;
}[] => {
  const filtered = candles.filter((x) => x.openTime <= openTime).slice(-100);
  return calcRSIFromCandles(candles);
};

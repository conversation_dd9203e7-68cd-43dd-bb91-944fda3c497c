import { pluck, reverse } from 'ramda';
import { MFI } from 'technicalindicators';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';
import { MFIInput } from 'technicalindicators/declarations/volume/MFI';

const calc = (
  high: number[],
  low: number[],
  close: number[],
  volume: number[],
  period: number = 14,
): number[] => {
  const input: MFIInput = {
    high,
    low,
    close,
    volume,
    period,
  };

  return MFI.calculate(input);
};

export const calcMFIFromCandles = (
  candles: DbCandleStickDocument[],
  period: number = 14,
): {
  mfi: number;
  openTime: number;
  coinPriceUsd: number;
}[] => {
  const high = pluck('high')(candles);
  const low = pluck('low')(candles);
  const close = pluck('close')(candles);
  const volume = pluck('quoteVolume')(candles);

  const resultValues = calc(high, low, close, volume, period);
  const result = [];
  const candlesReverse = reverse(candles);
  const resultReverse = reverse(resultValues);

  resultReverse.forEach((value, idx) => {
    result.push({
      mfi: value,
      openTime: candlesReverse.at(idx).openTime,
      coinPriceUsd: candlesReverse.at(idx).open,
    });
  });

  return reverse(result);
};

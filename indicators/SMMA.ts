// required indicators

import SMA from './SMA';

const SMMA = function (weight) {
  this.input = 'price';
  this.sma = new SMA(weight);
  this.weight = weight;
  this.prices = [];
  this.result = 0;
  this.age = 0;
};

SMMA.prototype.update = function (price) {
  this.prices = this.prices.slice(-100);

  this.prices.push(price);

  if (this.prices.length < this.weight) {
    this.sma.update(price);
  } else if (this.prices.length === this.weight) {
    this.sma.update(price);
    this.result = this.sma.result;
  } else {
    this.result = (this.result * (this.weight - 1) + price) / this.weight;
  }

  this.age++;
};

export default SMMA;

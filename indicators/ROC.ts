import { pluck, reverse } from 'ramda';
import { ROC } from 'technicalindicators';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';
import { ROCInput } from 'technicalindicators/declarations/indicators';

const calc = (close: number[], period: number = 12): number[] => {
  const input: ROCInput = {
    values: close,
    period,
  };

  return ROC.calculate(input);
};

export const calcROCFromCandles = (
  candles: DbCandleStickDocument[],
  period: number = 12,
): {
  roc: number;
  openTime: number;
  coinPriceUsd: number;
}[] => {
  const close = pluck('close')(candles);

  const resultValues = calc(close, period);
  const result = [];
  const candlesReverse = reverse(candles);
  const resultReverse = reverse(resultValues);

  resultReverse.forEach((value, idx) => {
    result.push({
      roc: value,
      openTime: candlesReverse.at(idx).openTime,
      coinPriceUsd: candlesReverse.at(idx).open,
    });
  });

  return reverse(result);
};

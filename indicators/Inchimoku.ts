import { pluck, reverse } from 'ramda';
import { IchimokuCloud } from 'technicalindicators';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';
import {
  IchimokuCloudInput,
  IchimokuCloudOutput,
} from 'technicalindicators/declarations/ichimoku/IchimokuCloud';

const calc = (
  high: number[],
  low: number[],
  conversionPeriod: number = 9,
  basePeriod: number = 26,
  spanPeriod: number = 52,
  displacement: number = 26,
): IchimokuCloudOutput[] => {
  const input: IchimokuCloudInput = {
    high,
    low,
    conversionPeriod,
    basePeriod,
    spanPeriod,
    displacement,
  };

  return IchimokuCloud.calculate(input);
};

export const calcIchimokuFromCandles = (
  candles: DbCandleStickDocument[],
  conversionPeriod: number = 9,
  basePeriod: number = 26,
  spanPeriod: number = 52,
  displacement: number = 26,
): {
  ichimocu: {
    conversion: number;
    base: number;
    spanA: number;
    spanB: number;
  };
  openTime: number;
  coinPriceUsd: number;
}[] => {
  const high = pluck('high')(candles);
  const low = pluck('low')(candles);

  const resultValues = calc(
    high,
    low,
    conversionPeriod,
    basePeriod,
    spanPeriod,
    displacement,
  );
  const result = [];
  const candlesReverse = reverse(candles);
  const resultReverse = reverse(resultValues);

  resultReverse.forEach((value, idx) => {
    result.push({
      ichimocu: value,
      openTime: candlesReverse.at(idx).openTime,
      coinPriceUsd: candlesReverse.at(idx).open,
    });
  });

  return reverse(result);
};

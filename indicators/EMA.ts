import { pluck, reverse } from 'ramda';
import { EMA } from 'technicalindicators';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';

const calcEMA = (openValues: number[], period: number): number[] => {
  const input = {
    values: openValues,
    period: period,
  };

  return EMA.calculate(input);
};

export const calcEMARangeFromTimestampNoFilter = (
  candles: DbCandleStickDocument[],
  period: number,
): {
  emaValue: number;
  openTime: number;
  coinPriceUsd: number;
}[] => {
  const open = pluck('open')(candles);

  const emaValues = calcEMA(open, period);
  const result = [];
  const candlesReverse = reverse(candles);
  const emaReverse = reverse(emaValues);

  emaReverse.forEach((emaValue, idx) => {
    result.push({
      emaValue,
      openTime: candlesReverse.at(idx).openTime,
      coinPriceUsd: candlesReverse.at(idx).open,
    });
  });

  return reverse(result);
};

import { pluck, reverse } from 'ramda';
import { ADX } from 'technicalindicators';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';
import { ADXOutput } from 'technicalindicators/declarations/generated';

const calcADX = (
  close: number[],
  high: number[],
  low: number[],
  period: number,
): ADXOutput[] => {
  const input = {
    close: close,
    high: high,
    low: low,
    period: period,
  };

  return ADX.calculate(input);
};

export const calcADXRangeFromTimestampNoFilter = (
  candles: DbCandleStickDocument[],
  period: number = 14,
): {
  adxValue: number;
  openTime: number;
  coinPriceUsd: number;
}[] => {
  const close = pluck('close')(candles);
  const high = pluck('high')(candles);
  const low = pluck('low')(candles);

  const adxValues = calcADX(close, high, low, period);
  const result = [];
  const candlesReverse = reverse(candles);
  const adxReverse = reverse(adxValues);

  adxReverse.forEach((adxValue: ADXOutput, idx) => {
    result.push({
      adxValue: !Number.isNaN(adxValue.adx) ? adxValue.adx : null,
      openTime: candlesReverse.at(idx).openTime,
      coinPriceUsd: candlesReverse.at(idx).open,
    });
  });

  return reverse(result);
};

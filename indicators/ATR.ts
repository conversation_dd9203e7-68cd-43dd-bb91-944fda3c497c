import { pluck, reverse } from 'ramda';
import { ATR, MFI } from 'technicalindicators';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';
import { MFIInput } from 'technicalindicators/declarations/volume/MFI';
import { ATRInput } from 'technicalindicators/declarations/indicators';

const calc = (
  high: number[],
  low: number[],
  close: number[],
  period: number = 14,
): number[] => {
  const input: ATRInput = {
    high,
    low,
    close,
    period,
  };

  return ATR.calculate(input);
};

export const calcATRFromCandles = (
  candles: DbCandleStickDocument[],
  period: number = 14,
): {
  atr: number;
  openTime: number;
  coinPriceUsd: number;
}[] => {
  const high = pluck('high')(candles);
  const low = pluck('low')(candles);
  const close = pluck('close')(candles);

  const resultValues = calc(high, low, close, period);
  const result = [];
  const candlesReverse = reverse(candles);
  const resultReverse = reverse(resultValues);

  resultReverse.forEach((value, idx) => {
    result.push({
      atr: value,
      openTime: candlesReverse.at(idx).openTime,
      coinPriceUsd: candlesReverse.at(idx).open,
    });
  });

  return reverse(result);
};

/**
 * The first value for the Smoothed Moving Average is calculated as a Simple Moving Average (SMA):

 SUM1=SUM (CLOSE, N)

 SMMA1 = SUM1/ N

 The second and subsequent moving averages are calculated according to this formula:

 SMMA (i) = (SUM1 – SMMA1+CLOSE (i))/ N

 Where:

 SUM1 – is the total sum of closing prices for N periods;
 SMMA1 – is the smoothed moving average of the first bar;
 SMMA (i) – is the smoothed moving average of the current bar (except the first one);
 CLOSE (i) – is the current closing price;
 N – is the smoothing period.
 */
import { EMA, SMA } from 'technicalindicators';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';
import { pluck, reverse } from 'ramda';
import { MAInput } from 'technicalindicators/declarations/indicators';

// required indicators
const SMA_ = function (weight) {
  this.weight = weight;
  this.prices = [];
  this.result = 0;
  this.age = 0;
};

SMA_.prototype.update = function (price) {
  this.prices[this.age % this.weight] = price;
  const sum = this.prices.reduce((a, b) => {
    return a + b;
  }, 0);
  this.result = sum / this.prices.length;
  this.age++;
};

export default SMA_;

const calc = (openValues: number[], period: number): number[] => {
  const input: MAInput = {
    values: openValues,
    period: period,
  };

  return SMA.calculate(input);
};

export const calcSMA = (
  candles: DbCandleStickDocument[],
  period: number,
): {
  smaValue: number;
  openTime: number;
  coinPriceUsd: number;
}[] => {
  const open = pluck('open')(candles);

  const smaValues = calc(open, period);
  const result = [];
  const candlesReverse = reverse(candles);
  const smaReverse = reverse(smaValues);

  smaReverse.forEach((smaValue, idx) => {
    result.push({
      smaValue,
      openTime: candlesReverse.at(idx).openTime,
      coinPriceUsd: candlesReverse.at(idx).open,
    });
  });

  return reverse(result);
};

import { SMA } from 'technicalindicators';
import { last } from 'ramda';
import { MAInput } from 'technicalindicators/declarations/indicators';
import { DbCandleStick } from '@app/database/db-candlesticks/db-candlesticks.model';

/**
 */
const calcMA = (prices): number[] => {
  const input: MAInput = {
    period: 20,
    values: prices,
  };

  return SMA.calculate(input);
};

export const calcMAFromTimestamp = (
  openTime,
  candles: DbCandleStick[],
): number => {
  const prices = candles
    .filter((x) => x.openTime <= openTime)
    .slice(-50)
    .map((x) => x.close);

  const output = calcMA(prices);

  return last(output);
};

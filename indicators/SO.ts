import { pluck, reverse } from 'ramda';
import { Stochastic } from 'technicalindicators';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';
import {
  StochasticInput,
  StochasticOutput,
} from 'technicalindicators/declarations/generated';

const calcSO = (
  low: number[],
  high: number[],
  close: number[],
  period: number,
  signalPeriod: number,
): StochasticOutput[] => {
  const input: StochasticInput = {
    period,
    low,
    high,
    close,
    signalPeriod,
  };

  return Stochastic.calculate(input);
};

export const calcSOFromCandles = (
  candles: DbCandleStickDocument[],
  period: number = 14,
  signalPeriod: number = 3,
): {
  k: number;
  d: number;
  openTime: number;
  coinPriceUsd: number;
}[] => {
  const low = pluck('low')(candles);
  const high = pluck('high')(candles);
  const close = pluck('close')(candles);

  const resultValues = calcSO(low, high, close, period, signalPeriod);
  const result = [];
  const candlesReverse = reverse(candles);
  const resultReverse = reverse(resultValues);

  resultReverse.forEach((value: StochasticOutput, idx) => {
    result.push({
      k: value.k,
      d: value.d,
      openTime: candlesReverse.at(idx).openTime,
      coinPriceUsd: candlesReverse.at(idx).open,
    });
  });

  return reverse(result);
};

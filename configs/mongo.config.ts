import { ConfigService } from '@nestjs/config';
import { MongooseModuleOptions } from '@nestjs/mongoose';
import { Logger } from '@nestjs/common';
import { DatabaseModule } from '@app/database';

const DB_HOST_CONFIG = 'DB_HOST';

export const getMongoConfig = async (
  configService: ConfigService,
): Promise<MongooseModuleOptions> => {
  const logger = new Logger(DatabaseModule.name);

  const connectionString = configService.get(DB_HOST_CONFIG);
  logger.log(`Using connection string: ${connectionString}`);

  return {
    uri: connectionString,
    socketTimeoutMS: 300000,
    connectTimeoutMS: 30000,
  };
};

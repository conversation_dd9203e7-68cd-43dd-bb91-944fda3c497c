{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@app/database": ["libs/database/src"], "@app/database/*": ["libs/database/src/*"], "@app/binance-api": ["libs/binance-api/src"], "@app/binance-api/*": ["libs/binance-api/src/*"], "@app/bybit-api": ["libs/bybit-api/src"], "@app/bybit-api/*": ["libs/bybit-api/src/*"], "@app/gateio-api": ["libs/gateio-api/src"], "@app/gateio-api/*": ["libs/gateio-api/src/*"], "@app/indicators": ["libs/indicators/src"], "@app/indicators/*": ["libs/indicators/src/*"], "@app/send-mail": ["libs/send-mail/src"], "@app/send-mail/*": ["libs/send-mail/src/*"], "@app/auto-bot-processor": ["libs/auto-bot-processor/src"], "@app/auto-bot-processor/*": ["libs/auto-bot-processor/src/*"]}}}